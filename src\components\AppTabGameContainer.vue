<script setup lang="ts" name="app-tab-game-container">
import { GameNavEnum } from "~/types/common";

const appStore = useAppStore();
// 只保留需要的引用
const { isSmallGameIcon } = storeToRefs(appStore);

// 定义props
interface Props {
  filterType?: string;
  id?: GameNavEnum;
}

const props = withDefaults(defineProps<Props>(), {
  id: GameNavEnum.Quente,
});

// 收藏游戏数据
const favedGames = ref<any[]>([]);

// 获取收藏游戏列表
const { run: getFavedGames } = useRequest(
  () =>
    ApiLoveGameList({
      ty: "fav",
      platform_id: "0",
      hot: 0,
      game_type: props.id,
    }),
  {
    onSuccess: (data) => {
      favedGames.value = data || [];
    },
  }
);

// 标签项数据
const tabData = ref([
  {
    label: "Popular",
    value: "0",
  },
  {
    label: "Recente",
    value: "1",
  },
  {
    label: "Favoritos",
    value: "2",
  },
]);

// 当前选中的标签
const activeTab = ref("0");

// 监听标签变化
watch(activeTab, (newValue) => {
  console.log("Tab changed to:", newValue);
  // 当切换到"收藏"标签时获取收藏数据
  if (newValue === "2") {
    getFavedGames();
  }
});

// 标签切换回调
const onTabChange = () => {
  // 可以在这里执行额外操作
};

// 用于传递给AppIndexRecFavGameContainer的filter-type
const getFilterType = computed(() => {
  // 优先使用从props传入的filterType
  if (props.filterType) return props.filterType;

  // 如果没有传入，则根据当前选中的标签决定
  if (activeTab.value === "1") return "rec";
  if (activeTab.value === "2") return "love";
  return "";
});
</script>

<template>
  <div class="app-tab-game-container">
    <!-- 顶部标签导航 -->
    <div class="tab-header">
      <AppTab
        :list-data="tabData"
        v-model="activeTab"
        @click="onTabChange"
        :height="88"
      ></AppTab>
    </div>

    <!-- 游戏内容区域 -->
    <div class="tab-content">
      <!-- Popular 内容 -->
      <div v-if="activeTab === '0'" class="content-popular">
        <AppIndexGameContainer
          :id="id"
          class="game-container-list"
          :filter-type="getFilterType"
          :showTitle="false"
        />
      </div>

      <!-- Recente 内容 -->
      <div v-else-if="activeTab === '1'" class="content-recente">
        <AppGameRecente class="game-container-list" :showTitle="false" />
      </div>

      <!-- Favoritos 内容 -->
      <div v-else-if="activeTab === '2'" class="content-favoritos">
        <AppIndexRecFavGameContainer
          :id="id"
          :filter-type="getFilterType"
          :showTitle="false"
        />
      </div>
    </div>
    <div
      style="
        height: 1px;
        background-color: var(--theme-color-line);
        position: relative;
        border-radius: 1.333333vw;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 20px;
        margin-bottom: 20px;
      "
    ></div>
  </div>
</template>

<style lang="scss" scoped>
.app-tab-game-container {
  width: 100%;

  .tab-header {
    background-color: transparent;
    border-radius: 10px 10px 0 0;
    border-bottom: 1px solid var(--theme-text-color-lighten);
    margin-bottom: -1px;
    :deep(.app-tab) {
      .scroll .item {
        opacity: 0.7;

        &.active {
          opacity: 1;
          font-weight: bold;
        }
      }

      .after {
        // background-color: #ffffff;
      }
    }
  }

  .tab-content {
    width: 100%;

    .content-popular,
    .content-recente,
    .content-favoritos {
      width: 100%;
      min-height: 300px; /* 添加最小高度，确保即使没有内容也有足够空间 */
    }

    .no-data-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 60px 0;

      .icon {
        margin-bottom: 16px;
      }

      .no-data-text {
        color: #999;
        font-size: 16px;
      }
    }
  }
}
</style>
