<script setup lang="ts" name="promotion">
import { GameTypeEnum, GameHallTopEnum } from "~/types/common";
import arrowNextIcon from "/icons/svg/arrowNext.svg?raw";
import arrowPreIcon from "/icons/svg/arrowPre.svg?raw";

import searchIcon from "/icons/svg/search.svg?raw";
const router = useRouter();
const appStore = useAppStore();
const { isSmallGameIcon, gameItemPageNum } = storeToRefs(appStore);
//重连数据
const isGetDataFinish = ref(false);
// 分页的列表数据
const gameList = ref<any[]>([]);
const pageItemNum = ref(30);
const curPage = ref(1);
const maxPage = ref(1);
const total = ref(0);
const finished = ref(false);
const isShowVertical = ref(true);
const question = ref();
const selectItem = ref(1);
const selectGame = ref(GameHallTopEnum.Solts);
const left_arrow = ref(false);
const right_arrow = ref(false);
const ty = ref(3);
const platform_id = ref("0");
// 首页 获取推荐的 热门 游戏 100
// 首页 获取推荐的 收藏 游戏 101
// const { run: runGetSpecialRec, loading:listLoading } = useRequest(() => ApiGameHotList({ ty: 0, platform_id: '0', page_size: pageItemNum.value, page: curPage.value, is_fav:undefined }), {
//   manual: true,
//   onSuccess: (data) => {
//     isGetDataFinish.value =true
//     onSuccessPage(data)
//   },
// })

//
const { run: runGetNormalRec, loading: listLoading } = useRequest(
  () =>
    ApiGameRecList({
      ty: ty.value,
      platform_id: platform_id.value,
      page_size: pageItemNum.value,
      page: curPage.value,
      is_fav: undefined,
    }),
  {
    manual: true,
    onSuccess: (data) => {
      isGetDataFinish.value = true;
      onSuccessPage(data);
    },
    onAfter: () => {},
  }
);

runGetNormalRec();

const onSuccessPage = (data: any) => {
  if (data) {
    if (curPage.value === 1) {
      total.value = data.t;
      maxPage.value = Math.ceil(data.t / pageItemNum.value);
      gameList.value = [];
    }
    if (data.d && data.d.length) {
      gameList.value = [...data.d];
      // gameList.value = gameList.value.concat(data.d)
    }

    if (curPage.value == 1) {
      left_arrow.value = false;
    } else {
      left_arrow.value = true;
    }

    if (curPage.value >= maxPage.value) {
      right_arrow.value = false;
    } else {
      right_arrow.value = true;
    }

    // if ((curPage.value === 1 && gameList.value.length < pageItemNum.value) || (gameList.value.length && total.value > 0 && gameList.value.length >= total.value)) {
    //   finished.value = true;
    // } else {
    //   finished.value = false
    // }
  }
};

appStore.setFooterDialogVisble(false);

//下一页
const goPage = (type: number) => {
  if (curPage.value + type <= maxPage.value && curPage.value + type >= 1) {
    curPage.value = curPage.value + type;
    runGetNormalRec();
    // runGetSpecialRec()
  }
};

//查找
const search = () => {
  console.log("search");
};

//时间查找
const searchTime = (type: number) => {
  console.log("searchTime");
  selectItem.value = type;
};

//返回按钮
const callback = () => {
  console.log("callback");
  appStore.setFooterDialogVisble(true);
  router.go(-1);
};

//获取游戏
const getGameData = (platformId: any) => {
  if (platformId == GameHallTopEnum.Solts) {
    if (platform_id.value == "0") {
      return;
    }
  }

  if (platformId == platform_id.value) {
    return;
  }

  if (platformId == GameHallTopEnum.Solts) {
    ty.value = 3;
    platform_id.value = "0";
  } else {
    platform_id.value = platformId;
    ty.value = 0;
  }

  selectGame.value = platformId;
  //
  curPage.value = 1;
  runGetNormalRec();
};

const tabListA = ref<any[]>([
  {
    src: "/icons/nav_600.png",
    src1: "/icons/nav_600.png",
    platform_id: GameHallTopEnum.Solts,
    cateName: "0 games",
    lastName: "Slots",
  },
  {
    src: "/icons/nav_101.png",
    platform_id: GameTypeEnum.platform_pg,
    cateName: "0 games",
    lastName: "PG",
  },
  {
    src: "/icons/nav_201.png",
    platform_id: GameTypeEnum.platform_pp,
    cateName: "0 games",
    lastName: "PP",
  },
  {
    src: "/icons/nav_1103.png",
    platform_id: GameTypeEnum.platform_tada,
    cateName: "0 games",
    lastName: "Tada",
  },
  {
    src: "/icons/nav_903.png",
    platform_id: GameTypeEnum.platform_haba,
    cateName: "0 games",
    lastName: "Haba",
  },
  {
    src: "/icons/nav_803.png",
    platform_id: GameTypeEnum.platform_yesbingo,
    cateName: "0 games",
    lastName: "YesBingo",
  },
  {
    src: "/icons/nav_703.png",
    platform_id: GameTypeEnum.platform_fc,
    cateName: "0 games",
    lastName: "Fc",
  },
  {
    src: "/icons/nav_403.png",
    platform_id: GameTypeEnum.platform_evo,
    cateName: "0 games",
    lastName: "EVO",
  },
  {
    src: "/icons/nav_1003.png",
    platform_id: GameTypeEnum.platform_hacksaw,
    cateName: "0 games",
    lastName: "Hacksaw",
  },
  {
    src: "/icons/nav_503.png",
    platform_id: GameTypeEnum.platform_jdb,
    cateName: "0 games",
    lastName: "JDB",
  },
]);

const tabListB = ref<any[]>([
  {
    src: "/icons/nav_600.png",
    src1: "/icons/nav_600.png",
    platform_id: "Blockchain",
    cateName: "0 games",
    lastName: "Blockchain",
  },
  {
    src: "/icons/Blockchain/jdb.webp",
    platform_id: "bjdb",
    cateName: "0 games",
    lastName: "JDB",
  },
  {
    src: "/icons/Blockchain/wg.webp",
    platform_id: "bwg",
    cateName: "0 games",
    lastName: "WG",
  },
  // { src: '/icons/nav_403.png',platform_id: GameTypeEnum.platform_evo ,cateName:"0 games",lastName:"EVO"},
  {
    src: "/icons/Blockchain/t1.webp",
    platform_id: "bt1",
    cateName: "0 games",
    lastName: "T1",
  },
  {
    src: "/icons/Blockchain/tada.webp",
    platform_id: "btada",
    cateName: "0 games",
    lastName: "TADA",
  },
  // { src: '/icons/nav_1003.png',platform_id: GameTypeEnum.platform_hacksaw ,cateName:"0 games",lastName:"Hacksaw"},
  {
    src: "/icons/Blockchain/jili.webp",
    platform_id: "bjili",
    cateName: "0 games",
    lastName: "JILI",
  },
  // { src: '/icons/nav_1103.png',platform_id: GameTypeEnum.platform_tada ,cateName:"0 games",lastName:"Tada"},
  {
    src: "/icons/Blockchain/xgame.webp",
    platform_id: "bxgame",
    cateName: "0 games",
    lastName: "XGAME",
  },

  // { src: '/icons/nav_903.png',platform_id: GameTypeEnum.platform_haba ,cateName:"0 games",lastName:"Haba"},
]);

const tabListC = ref<any[]>([
  {
    src: "/icons/nav_600.png",
    src1: "/icons/nav_600.png",
    platform_id: GameHallTopEnum.Solts,
    cateName: "0 games",
    lastName: "Blockchain",
  },
  {
    src: "/icons/nav_101.png",
    platform_id: GameTypeEnum.platform_pg,
    cateName: "0 games",
    lastName: "JDB",
  },
  {
    src: "/icons/nav_201.png",
    platform_id: GameTypeEnum.platform_pp,
    cateName: "0 games",
    lastName: "WG",
  },
  {
    src: "/icons/nav_503.png",
    platform_id: GameTypeEnum.platform_jdb,
    cateName: "0 games",
    lastName: "T1",
  },
  {
    src: "/icons/nav_703.png",
    platform_id: GameTypeEnum.platform_fc,
    cateName: "0 games",
    lastName: "TADA",
  },
  {
    src: "/icons/nav_1103.png",
    platform_id: GameTypeEnum.platform_tada,
    cateName: "0 games",
    lastName: "JILI",
  },
  {
    src: "/icons/nav_803.png",
    platform_id: GameTypeEnum.platform_yesbingo,
    cateName: "0 games",
    lastName: "XGAME",
  },
]);

const list = ref([
  {
    img: "/img/AoVivo/custom_BRL1.avif",
    platform_id: GameTypeEnum.platform_pg,
    key: 0,
  },
  {
    img: "/img/AoVivo/custom_BRL2.avif",
    platform_id: GameTypeEnum.platform_pp,
    key: 1,
  },
  // { img: '/img/tada1.webp',platform_id: GameTypeEnum.platform_tada, key: 2},
  {
    img: "/img/AoVivo/custom_BRL3.avif",
    platform_id: GameTypeEnum.platform_jili,
    key: 2,
  },
  // { img: '/img/blockchain/custom_BRL4.avif',platform_id: GameTypeEnum.platform_yesbingo, key: 4},
  // { img: '/img/blockchain/custom_BRL5.avif',platform_id: GameTypeEnum.platform_fc, key: 5},
  // { img: '/img/blockchain/custom_BRL6.avif',platform_id: GameTypeEnum.platform_jdb, key: 8},
]);

if (router.currentRoute.value.query.platform_id) {
  selectGame.value = router.currentRoute.value.query.platform_id;
  getGameData(selectGame.value);
}

const gameSlotsInnerStyle: any = {
  margin: isSmallGameIcon.value
    ? `var(--app-npx-10) var(--app-npx-20)`
    : `var(--app-npx-15) 0`,
};
</script>
<template>
  <div class="app-index-game-container">
    <AppPageTitle
      :backFunc="callback"
      left-arrow
      title="AoVivo"
      title-weight="700"
    />
    <div class="content">
      <div class="procurar">
        <div class="searchParent">
          <input
            v-model="question"
            class="search"
            placeholder="Procurar jogos"
          />
          <!-- <AppImage
            class="select-img"
            src="/img/index/header_select"
            alt=""
            maxlength="30"
          /> -->
          <div class="select-img" v-html="searchIcon" @click="search"></div>
        </div>
      </div>

      <div class="rightDiv">
        <div class="button_div">
          <div
            class="button_item"
            :class="{ active: selectItem == 1 }"
            @click="searchTime(1)"
          >
            Tudo
          </div>
          <div
            class="button_item"
            :class="{ active: selectItem == 2 }"
            @click="searchTime(2)"
          >
            Recente
          </div>
          <div
            class="button_item"
            :class="{ active: selectItem == 3 }"
            @click="searchTime(3)"
          >
            Favoritos
          </div>
        </div>

        <div class="app-maps game-container">
          <div ref="scrollView" class="content">
            <AppSingleSlotItem
              v-for="item in list"
              :data="item"
              class="slot-item"
            />
          </div>
        </div>

        <div v-if="false" class="app-maps game-container">
          <div
            ref="scrollView"
            class="content2"
            :class="{
              versionContent: isShowVertical,
              'is-empty-loading': !gameList?.length,
            }"
            :style="gameSlotsInnerStyle"
          >
            <template v-if="gameList && gameList.length < 0">
              <AppGameItem
                v-if="isSmallGameIcon"
                class="game-item"
                :itemWidth="160"
                :item-height="212"
                v-for="(item, idx) in gameList"
                :key="item.id + idx"
                :data="{ ...item }"
              />
              <AppGameItemBig
                v-else
                class="game-item"
                :itemWidth="180"
                :item-height="212"
                :imgWidth="160"
                :imgHeight="212"
                v-for="(item, idx) in gameList"
                :key="item.id + idx"
                :data="{ ...item }"
              />
            </template>
            <div v-else>
              <div v-if="listLoading" class="last-loading">
                <AppSpinner :size="100" :stroke-width="10" color="#1373EF" />
              </div>
            </div>
          </div>
        </div>

        <div class="more" v-if="!finished">
          <!-- <AppImage
            class="popular_arr"
            :src="`/icons/popular_left${left_arrow ? '_select' : ''}`"
            alt=""
            @click="goPage(-1)"
            maxlength="30"
          /> -->
          <!-- <div
            class="popular_arr"
            @click="goPage(-1)"
            v-html="arrowPreIcon"
          ></div>
          <label class="popular_text"> {{ curPage }}/{{ maxPage }}</label>
          
          <div
            class="popular_arr"
            @click="goPage(1)"
            v-html="arrowNextIcon"
          ></div> -->
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.app-index-game-container {
  height: 100vh;
  // background-color: #7db39e;
}

.content {
  position: absolute;
  width: 100%;
  min-height: calc(100% - 90px);
  // height: calc(100% - 100px);
  background-color: var(--theme-main-bg-color);
  overflow-x: hidden;
}

.procurar {
  width: 100%;
  padding-bottom: 0px;
  .searchParent {
    margin: 0 auto;
    margin-top: 25px;
    width: 700px;
    height: 52px;
    border: 1px solid;
    // background:#164633;
    border-color: var(--theme-color-line);
    border-radius: 26px;
  }
  .search {
    display: inline;
    margin: 0 auto;
    padding-left: 20px;
    width: 600px;
    height: 52px;
    background-color: transparent;
    color: #ffffff;
    font-size: 24px;
  }

  .select-img {
    position: absolute;
    width: 27px;
    top: 4.7vw;
    right: 7vw;
  }
  .search::placeholder {
    color: var(--theme-text-color-lighten); /* 灰色 */
  }
}

.leftDiv {
  display: inline-block;
  width: 187px;
  // background-color: #FFFFFF;
  height: calc(100vh - 250px);
  overflow: auto;
  scrollbar-width: none;
  // height: 1250px;
}

.leftDivBtn {
  width: 150px;
  height: 104px;
  background: var(--theme-sub-bg-color);
  border-radius: 12px;
  margin: 0 auto;
  margin-top: 25px;
  margin-left: 30px;
  padding-top: 5px;
  color: var(--theme-text-color);
  box-shadow: 2px 0.4px 0 var(--theme-aside-no-active-box-shadow);

  &.active {
    background: var(--theme-primary-color);
    color: var(--theme-font-on-background-color);
    box-shadow: var(--theme-aside-active-box-shadow);
  }

  .icon_leftBtn {
    display: block;
    margin: 0 auto;
    width: 64px;
    margin-top: 8px;
  }

  .icon_leftBtn2 {
    display: block;
    margin: 0 auto;
    width: 64px;
    margin-top: 8px;
  }

  .text {
    display: inline-block;
    font-size: 24px;
    width: 150px;
    text-align: center;
  }
}

.rightDiv {
  // float: right;
  height: calc(100vh - 250px);
  // width: 562px;
  // background-color: #FFFFFF;
}

.button_div {
  // background-color: #FFFFFF;
  margin: auto;
  width: 500px;
  margin-top: 25px;
  margin-bottom: 25px;
  margin-left: 18px;
  .button_item {
    margin-right: 20px;
    display: inline-block;
    width: 140px;
    height: 56px;
    background: var(--theme-main-bg-color);
    border: 2px solid;
    border-color: var(--theme-color-line);
    border-radius: 12px;
    color: var(--theme-text-color);
    font-size: 24px;
    text-align: center;
    line-height: 56px;
    &.active {
      background: var(--theme-primary-color);
      color: var(--theme-font-on-background-color);
      // border-radius:6px;
    }
  }
}

.app-maps.game-container {
  position: relative;
    /* margin-left: -12.666667vw; */
    width: 90%;
    left: 50%;
    transform: translateX(-50%);
}

.content2 {
  max-height: calc(100vh - 360px);
  display: flex;
  flex-wrap: wrap;
  // grid-column-gap: 5px;
  // grid-row-gap: 5px;
  // scroll-snap-type: x;
  overflow: auto;
  // align-items: start;
  // justify-items: start;
  min-height: 350px;
  --cardRowSpacing: 22px;
  // padding-top: 10px;
  // padding-left: 10px;
  scrollbar-width: none;
  &::-webkit-scrollbar {
    display: none;
  }
  &.one-row {
    grid-template-rows: auto;
    justify-content: flex-start;
    height: 295px;
  }
  // .left {
  //   width: 12px;
  //   scroll-snap-align: start;
  // }
  &.is-empty-loading {
    height: calc(100vh - 360px);
    align-items: center;
    justify-items: center;
    justify-content: center;
  }

  &.overflow-hidden {
    overflow: hidden;
  }
  &.flow-row {
    grid-auto-flow: row;
  }

  .last-loading {
    grid-row-start: 1;
    grid-row-end: 2;
    width: 580px;
    height: 100%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.more {
  // position: absolute;
  position: fixed;
  // background-color: #cdeddf;
  width: 250px;
  height: 48px;
  display: flex;
  transform: translate(-50%, -50%);
  left: 63%;
  top: 97.5%;

  .popular_arr {
    width: 48px;
  }

  .popular_text {
    display: inline-block;
    width: 150px;
    // height: 48px;
    text-align: center;
    line-height: 48px;
    color: var(--theme-text-color);
    // margin-top: 8px
    // background-color: #2a815f;
  }
}

.game-item {
  // padding-left: 15px;
  // padding-right: 15px;
}
//热门竖版显示
.versionContent {
  // grid-template-columns:auto auto auto;
  // grid-template-rows: auto auto;

  // grid-template-columns: repeat(3, 1fr); /* 创建三个等宽的列 */
  // grid-template-rows: repeat(3, 230px); /* 创建两行固定高度为100px的网格 */
}
</style>
