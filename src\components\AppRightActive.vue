<script lang="ts" setup>
const router = useRouter();

const appStore = useAppStore();
const { appRightActiveVisible, secletType } = storeToRefs(appStore);
import { CfgSuspensionImagesParam } from "~/core/http/api"
import rocketIcon from "/icons/svg/rocket.svg?raw"

const listActiveData = ref<listActiveData[]>([]);
// let i = 0
// for (i; i < 1; i++) {
//   const temp1: CfgSuspensionImagesParam = {
//     id: 1,
//     images: "/icons/ActiveImg3511614282972711.gif",
//     jump_type: 1,
//     sort: i + 1,
//     status: 1,
//     display_method: 1,
//     url: "/promotion-detail/recharge-rewards",
//   }
//   const temp2: CfgSuspensionImagesParam = {
//     id: 2,
//     images: "/icons/ActiveIm.gif",
//     jump_type: 1,
//     sort: i + 2,
//     status: 1,
//     display_method: 2,
//     url: "/promotion-detail/invitation-rewards",
//   }
//   const temp3: CfgSuspensionImagesParam = {
//     id: 3,
//     images: "/icons/ActiveImg3511614282972711.gif",
//     jump_type: 1,
//     sort: i + 3,
//     status: 1,
//     display_method: 2,
//     url: "/promotion-detail/invitation-rewards",
//   }
//   const temp4: CfgSuspensionImagesParam = {
//     id: 4,
//     images: "/icons/ActiveIm.gif",
//     jump_type: 1,
//     sort: i + 4,
//     status: 1,
//     display_method: 1,
//     url: "/promotion-detail/invitation-rewards",
//   }
//   listActiveData.value.push(temp1)
//   listActiveData.value.push(temp2)
//   listActiveData.value.push(temp3)
//   listActiveData.value.push(temp4)
// }
// console.log("listActiveData", listActiveData);

const { run: runApiActiveSwitchList } = useRequest(() => ApiActiveSwitchList(), {
  manual: true,
  onError: (data) => {
  },
  onSuccess: (data) => {
    if (data) {
      console.log("runApiActiveSwitchList", data)
      if (data?.suspension_images_cfg) {
        listActiveData.value = [...listActiveData.value, ...data?.suspension_images_cfg!]
        console.log("listActiveData", listActiveData);
      }
    }
  }
})
runApiActiveSwitchList();
const sortListActiveData = computed(() => {
  return listActiveData.value?.filter(item => item.status == 1) .sort((a, b) => a.sort - b.sort);
})

const display1List = computed(() => {
  return sortListActiveData.value?.filter(item => item.display_method == 1)
});
const display2List = computed(() => {
  return sortListActiveData.value?.filter(item => item.display_method == 2)
});
console.log("sortListActiveData", sortListActiveData);

// console.log("sortListActiveData", display1List, display2List);


function itemClick(item: CfgSuspensionImagesParam) {
  console.log("imgClick =", item)
  if (!item) return;
  switch (item.jump_type) {
    case 1:
      // if (item.url.includes('vip')) {
      //   secletType.value = 2;
      // } else {
      //   router.push(item.url)
      // }
      router.push(item.url)
      break;
    case 2:
      window.open(item.url, '_blank')
      break;
  }
}

onMounted(() => {
  setTimeout(() => {
    // show.value = true
  }, 0);
});

const goTop = () => {
  console.log("app-index", document.querySelector(".app-index"));
  const dom: any = document.querySelector(".app-index");

  dom.scrollTo({
    top: 0,
    behavior: "smooth",
  });
};



</script>

<template>
  <div v-if="appRightActiveVisible" class="app-right">
    <ul class="menu">
      <li v-for="(item, index) in display1List" :key="index" class="menu-item" @click="() => itemClick(item)">
        <img class="drag_img" :src="item.images" />
      </li>
      <van-swipe class="active-swipe" :autoplay="3000" :duration="1000" :show-indicators="false">
        <van-swipe-item v-for="(item, index) in display2List" :key="index" @click="() => itemClick(item)">
          <img class="drag_img" :src="item.images" />
        </van-swipe-item>
      </van-swipe>
    </ul>
    <div @click="() => goTop()" class="app-index-go-top">
      <div class="img-box">
        <!-- <img src="/icons/goTop.png" /> -->
        <div style="width: 25px; height: 25px;" v-html="rocketIcon"></div>
      </div>
      <span>Top</span>
    </div>

  </div>
</template>

<style lang="scss" scoped>
@import "../theme/mixin.scss";

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 1;
}

.icon-img {
  width: 0;
  position: absolute;
  top: 20px;
}

.icon-imgCenter {
  position: absolute;
  width: 0;
  top: 20px;
}

ul {
  margin: 0;
  padding: 0;
}

li {
  list-style: none;
}

.app-right {
  z-index: 98;
  position: fixed;
  bottom: 20%;
  right: 0;
  width: 160px;
  // height: 220px;
  // background: url() no-repeat 100%/100%;
  // @include webp('/img/app-footer-bg.png');
  // background-size: 100% 100%;
  // background: var(--theme-btm-bg-color);
  text-align: center;
  color: #7d8aa2;
  font-size: 22px;
  padding-top: 10px;
  // box-shadow: 0px -1px 3px rgba(0, 0, 0, 0.1);

  ul {
    display: flex;
    justify-content: top;
    align-items: center;
    /* 垂直居中 */
    flex-direction: column;
    width: 100%;
    height: 100%;

    li.menu-item {
      flex: 1 1 0%;
    }
  }

  .active-swipe {
    width: 100%;
  }
}

.drag_img {
  width: 160px;
  pointer-events: none;
  // border-radius: 50%;
  filter: var(--cg-img-shadow);
}

.app-index-go-top {
  margin-top: 20px;
  background: #aa2faa;
  border-radius: 12px;
  display: none;
  margin-left: 22px;
  flex-direction: column;
  align-items: center;
  width: 108px;
  height: 108px;
  justify-content: center;
  background-color: var(--theme-main-bg-color);
  border: 1px solid var(--theme-color-line);
  cursor: pointer;
  .img-box {
    width: 48px;
    height: 48px;
    background: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    img {
      width: 80%;
      height: 80%;
    }
  }
  span {
    margin-top: 8px;
    font-size: 20px;
    color: var(--theme-text-color-lighten);
    text-align: center;
  }
}
</style>
