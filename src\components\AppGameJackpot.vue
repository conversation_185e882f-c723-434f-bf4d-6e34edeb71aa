<script setup lang="ts" name="app-game-tab">

  const labelNode = ref()
  let moneyTestMin = 0
  let moneyTestMax = 662922811.66

  //事件监听
  onMounted(() => {
      console.log("appGameMarquee加载")
     
      setTimeout(() => {
        startRun()
      }, 1000);
  })

  onBeforeUnmount(() => {
    console.log("appGameMarquee加载44")
  })


  const gotoGame = ()=>{
    console.log("跳转进游戏")
  }

  const startRun=()=>{
    const intervalId = setInterval(() => {
    if(labelNode && labelNode.value ){
        if(moneyTestMin>moneyTestMax){
          clearInterval(intervalId );//取消定时器
          return
        }
        let temp = moneyTestMax - moneyTestMin
        let randData = 0
        // if(temp>1000){
          randData = temp/10
        // }else if(temp>300){
        //   randData = temp/10
        // }else {
        //   randData = temp/10
        // }

        moneyTestMin+=randData
        labelNode.value.innerHTML=transf(moneyTestMin)
      }else{
        clearInterval(intervalId );//取消定时器
        console.log("卸载=====setInterval")
      }
    },200)

  }

  //数字转换
  const transf=(value:number, number = 2)=>{
    if (typeof value !== "number") {
      return 0;
    }
       
    let num = parseFloat(value).toFixed(number);

    if (!/^([+\-])?(\d+)(\.\d+)?$/.test(num)) {
        alert("wrong!");
        return num;
    }

    let a = RegExp.$1;
    let b = RegExp.$2;
    let c = RegExp.$3;
    let re = new RegExp().compile("(\\d)(\\d{3})(,|$)");
    while (re.test(b))
        b = b.replace(re, "$1,$2$3");



    b = b.replace(new RegExp(",", 'g'), ".");
    c = c.replace(".", ",");
    return a + "" + b + "" + c;
    
  }
 


</script>

<template>
 
  <div class="content">
    
    <div class="center"  @click="gotoGame">
        <label ref="labelNode"> 0.00 </label>
    </div>

  </div>
</template>

<style lang="scss" scoped>
@import '../theme/mixin.scss';
.content {
  width: 100%;
  height:168px;
  margin-bottom: 10px;
}

.center {
  margin: 0 auto;
  width:708px;
  height:168px;
  background: url('/icons/jackpot.webp') ;
  background-size: 100% 100%; 
  margin-top: 0px;

  label{
    display: block;
    margin: 0 auto;
    padding-top: 11.4vw;
    height:80px ;
    width: 700px;
    text-align: center;
    color: #FFFFff;
    font-size: 4.639333vw;
    font-weight: 900;
    // font-style: italic;
    letter-spacing: -0.2vw;
  }

}




</style>



