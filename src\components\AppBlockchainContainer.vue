<script setup lang="ts" name="app-single-slots-container">
import { GameTypeEnum, GameNavEnum } from "~/types/common";
interface Props {
  id: GameNavEnum;
  filterType?: string;
  favType?: string;
}
const props = defineProps<Props>();
const scrollView = ref();
const router = useRouter();
const finished = ref(false);
const list = ref([
  {
    img: "/img/blockchain/custom_BRL1.avif",
    platform_id: 'bjdb',
    key: 0,
  },
  {
    img: "/img/blockchain/custom_BRL2.avif",
    platform_id: 'bwg',
    key: 1,
  },
  // { img: '/img/tada1.webp',platform_id: GameTypeEnum.platform_tada, key: 2},
  {
    img: "/img/blockchain/custom_BRL3.avif",
    platform_id: 'bt1',
    key: 2,
  },
]);

function gotoLeft() {
  scrollView.value?.scrollTo(
    scrollView.value.scrollLeft - scrollView.value.clientWidth,
    100
  );
}

function gotoRight() {
  scrollView.value?.scrollTo(
    scrollView.value.scrollLeft + scrollView.value.clientWidth,
    100
  );
}

function goPage() {
  console.log(887769);
  list.value.push(
    {
      img: "/img/blockchain/custom_BRL4.avif",
      platform_id: 'btada',
      key: 4,
    },
    {
      img: "/img/blockchain/custom_BRL5.avif",
      platform_id: 'bjili',
      key: 5,
    },
    {
      img: "/img/blockchain/custom_BRL6.avif",
      platform_id: 'bxgame',
      key: 8,
    }
  );
  finished.value = true;
}
</script>

<template>
  <div class="app-single-slots-container">
    <AppIndexTitle
      :id="id"
      @gotoLeft-by="gotoLeft"
      :isCallback="false"
      @gotoRight-by="gotoRight"
    />
    <div class="app-maps game-container">
      <div ref="scrollView" class="content">
        <AppSingleSlotItem
          v-for="item in list"
          :data="item"
          class="slot-item"
        />
      </div>
    </div>
    <div class="more" v-if="!finished">
      <label class="more-text"
        >A exibir 3 jogos entre 6
        {{ id == GameNavEnum.Slots ? "Slots" : "" }} jogos</label
      >
      <div class="more-text-carregar" @click="goPage">
        Carregar mais
        <AppImage class="more-text-img" src="/icons/downcarregar.webp" alt="" />
      </div>
    </div>
    <div
      style="
        height: 1px;
        background-color: var(--theme-color-line);
        position: relative;
        border-radius: 1.333333vw;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 20px;
        margin-bottom: 20px;
      "
    ></div>
  </div>
</template>

<style lang="scss" scoped>
.app-maps.game-container {
  margin-left: -12.5px;
  margin-right: -12.5px;
  border-radius: 0;
}
.app-maps {
  border-radius: 0px 30px 30px 30px;
  padding: 0 25px;
  padding-bottom: 8px;

  .content {
    display: flex;
    flex-direction: column;
    gap: 20px;
    overflow: auto;

    &::-webkit-scrollbar {
      display: none;
    }
  }
}
.more {
  text-align: center;
  padding-top: 25px;
  border-radius: 10px;

  .more-text {
    display: block;
    color: var(--theme-text-color-lighten);
    font-size: 18px;
  }

  .more-text-carregar {
    display: inline-block;
    color: var(--theme-text-color);
    font-size: 18px;
    margin-top: 5px;
  }

  .more-text-img {
    width: 14px;
    vertical-align: middle;
  }
}
</style>
