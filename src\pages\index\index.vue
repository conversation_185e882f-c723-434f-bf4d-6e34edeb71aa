<script setup lang="ts" name="home">
import {
  GameNavEnum,
  GameSoltTypeEnum,
  GameNewHallTopEnum,
} from "~/types/common";
import { nextTick } from "vue";
const router = useRouter();
const appStore = useAppStore();
const { isSmallGameIcon } = storeToRefs(appStore);

const popularGamesRef = ref(null);
const slotsGamesRef = ref(null);
const BlockchainGamesRef = ref(null);
const AoVivoGamesRef = ref(null);
const PescariaGamesRef = ref(null);
// const recenteGamesRef = ref(null);
const FavoritosGamesRef = ref(null);
const scrollIconId = ref(GameNewHallTopEnum.Popular);
const extraHeight = -15;

const openService = (url?: string) => {
  if (!url) return;
  window.open(url, "_blank");
};

const filterType = ref("");

const setFilterGameParam = (v: string) => {
  filterType.value = v;
};

// 定义事件处理函数，以便可以正确移除监听器
const handleScrollEvent = (event: CustomEvent) => {
  console.log("index.vue: Received scrollToSection event", event.detail);

  const { sectionId } = event.detail;
  // 使用 nextTick 确保 DOM 更新完成，Refs 可用
  nextTick(() => {
    scrollToSection(sectionId);
  });
};

// 定义滚动事件处理函数
const handleScroll = () => {
  console.log(window.scrollY);
};

// 组件加载时添加事件监听
onMounted(() => {
  window.addEventListener(
    "scrollToSection",
    handleScrollEvent as EventListener
  );

  // 同时监听 window 和容器的滚动
  window.addEventListener("scroll", handleScroll as EventListener);
  // const scrollContainer = document.querySelector(".app-index");
  // if (scrollContainer) {
  //   scrollContainer.addEventListener("scroll", handleScroll);
  // }
});

// 组件卸载时移除事件监听
onUnmounted(() => {
  window.removeEventListener(
    "scrollToSection",
    handleScrollEvent as EventListener
  );

  // 移除所有滚动事件监听器
  window.removeEventListener("scroll", handleScroll);
  const scrollContainer = document.querySelector(".app-index");
  if (scrollContainer) {
    scrollContainer.removeEventListener("scroll", handleScroll);
  }

  console.log("index.vue: Removed all event listeners");
});

// 滚动到指定区域方法
const scrollToSection = (sectionId: string) => {
  let targetElement: HTMLElement | null = null;
  // Revert: Use .app-index or document.documentElement
  const scrollContainer: HTMLElement =
    document.querySelector(".app-index") || document.documentElement;

  console.log("index.vue: scrollToSection called with ID:", sectionId);
  // Revert: Log the actual scroll container
  console.log("index.vue: Scroll container:", scrollContainer);

  // --- 恢复 'popular' 和 'slots' 的逻辑 ---
  console.log("index.vue: popularGamesRef.value:", popularGamesRef.value);
  console.log("index.vue: slotsGamesRef.value:", slotsGamesRef.value); // 恢复 slots 的日志

  try {
    if (sectionId === "popular" && popularGamesRef.value) {
      // 获取组件的根DOM元素

      targetElement =
        (popularGamesRef.value as any).$el || popularGamesRef.value;
    } else if (sectionId === "slots" && slotsGamesRef.value) {
      targetElement = (slotsGamesRef.value as any).$el || slotsGamesRef.value;
    } else if (sectionId === "Blockchain" && BlockchainGamesRef.value) {
      targetElement =
        (BlockchainGamesRef.value as any).$el || BlockchainGamesRef.value;
    } else if (sectionId === "AoVivo" && AoVivoGamesRef.value) {
      targetElement = (AoVivoGamesRef.value as any).$el || AoVivoGamesRef.value;
    } else if (sectionId === "Pescaria" && PescariaGamesRef.value) {
      targetElement =
        (PescariaGamesRef.value as any).$el || PescariaGamesRef.value;
    }
    // else if (sectionId === "recente" && recenteGamesRef.value) {
    //   targetElement =
    //     (recenteGamesRef.value as any).$el || recenteGamesRef.value;
    // }
  } catch (error) {
    console.error("Error accessing $el:", error);
    if (sectionId === "popular" && popularGamesRef.value) {
      targetElement = popularGamesRef.value as HTMLElement;
    } else if (sectionId === "slots" && slotsGamesRef.value) {
      targetElement = slotsGamesRef.value as HTMLElement;
    }
  }

  console.log("index.vue: Target element:", targetElement);

  if (targetElement && scrollContainer) {
    const scrollTop = scrollContainer.scrollTop;
    console.log("index.vue: scrollTop:", scrollTop);

    let scrollToPosition = 0; // Initialize
    let headerHeight = 0; // Used for fallback/other sections

    // --- Get Header Element and Height (for fallback or potentially other sections) ---
    const headerElement = document.querySelector("header.app-index-header");
    let headerRect = headerElement?.getBoundingClientRect();
    if (headerElement && headerRect) {
      headerHeight = (headerElement as HTMLElement).offsetHeight;
      // console.log(`index.vue: Found header element. Height: ${headerHeight}, Bottom: ${headerRect.bottom}`);
    } else {
      // If headerElement is null, headerRect will be undefined from the optional chaining above.
      // console.warn('index.vue: Could not find header element.');
    }

    // --- Calculate scroll position based on section ---
    if (sectionId === "popular") {
      const titleElement = targetElement.querySelector(".popular-title");
      if (titleElement && headerRect) {
        // Requires both title and header to exist
        const titleRect = titleElement.getBoundingClientRect();
        console.log(
          "index.vue: Found .popular-title. Calculating based on header bottom and title top."
        );
        scrollToPosition = scrollTop + titleRect.top - headerRect.bottom + 20;
        console.log(
          `index.vue: popular - scrollTop: ${scrollTop}, titleRect.top: ${titleRect.top}, headerRect.bottom: ${headerRect.bottom}`
        );
      } else {
        console.warn(
          "index.vue: Could not find .popular-title or header element for popular. Falling back."
        );
        const rect = targetElement.getBoundingClientRect();
        scrollToPosition = rect.top + scrollTop - headerHeight;
      }
      scrollIconId.value = GameNewHallTopEnum.Popular;
    } else if (sectionId === "slots") {
      // For slots, try to align with the first child element (AppIndexTitle)
      const slotsTitleElement = targetElement.firstElementChild;
      if (slotsTitleElement && headerRect) {
        const slotsTitleRect = slotsTitleElement.getBoundingClientRect();
        console.log(
          "index.vue: Found first child for slots. Calculating based on header bottom and title top."
        );
        scrollToPosition =
          scrollTop + slotsTitleRect.top - headerRect.bottom + extraHeight;
        console.log(
          `index.vue: slots - scrollTop: ${scrollTop}, slotsTitleRect.top: ${slotsTitleRect.top}, headerRect.bottom: ${headerRect.bottom}`
        );
      } else {
        console.warn(
          "index.vue: Could not find first child element or header element for slots. Falling back."
        );
        const rect = targetElement.getBoundingClientRect();
        scrollToPosition = rect.top + scrollTop - headerHeight;
      }
      scrollIconId.value = GameNewHallTopEnum.Slots;
    } else if (sectionId === "Blockchain") {
      // For slots, try to align with the first child element (AppIndexTitle)
      const BlockchainTitleElement = targetElement.firstElementChild;
      if (BlockchainTitleElement && headerRect) {
        const BlockchainTitleRect =
          BlockchainTitleElement.getBoundingClientRect();
        console.log(
          "index.vue: Found first child for slots. Calculating based on header bottom and title top."
        );
        scrollToPosition =
          scrollTop + BlockchainTitleRect.top - headerRect.bottom + extraHeight;
      } else {
        console.warn(
          "index.vue: Could not find first child element or header element for slots. Falling back."
        );
        const rect = targetElement.getBoundingClientRect();
        scrollToPosition = rect.top + scrollTop - headerHeight;
      }
      scrollIconId.value = GameNewHallTopEnum.Slots;
    } else if (sectionId === "AoVivo") {
      // For slots, try to align with the first child element (AppIndexTitle)
      const AoVivoTitleElement = targetElement.firstElementChild;
      if (AoVivoTitleElement && headerRect) {
        const AoVivoTitleRect = AoVivoTitleElement.getBoundingClientRect();
        console.log(
          "index.vue: Found first child for slots. Calculating based on header bottom and title top."
        );
        scrollToPosition =
          scrollTop + AoVivoTitleRect.top - headerRect.bottom + extraHeight;
      } else {
        console.warn(
          "index.vue: Could not find first child element or header element for slots. Falling back."
        );
        const rect = targetElement.getBoundingClientRect();
        scrollToPosition = rect.top + scrollTop - headerHeight;
      }
      scrollIconId.value = GameNewHallTopEnum.Slots;
    } else if (sectionId === "Pescaria") {
      // For slots, try to align with the first child element (AppIndexTitle)
      const PescariaTitleElement = targetElement.firstElementChild;
      if (PescariaTitleElement && headerRect) {
        const PescariaTitleRect = PescariaTitleElement.getBoundingClientRect();
        console.log(
          "index.vue: Found first child for slots. Calculating based on header bottom and title top."
        );
        scrollToPosition =
          scrollTop + PescariaTitleRect.top - headerRect.bottom + extraHeight;
      } else {
        console.warn(
          "index.vue: Could not find first child element or header element for slots. Falling back."
        );
        const rect = targetElement.getBoundingClientRect();
        scrollToPosition = rect.top + scrollTop - headerHeight;
      }
      scrollIconId.value = GameNewHallTopEnum.Slots;
    } else if (sectionId === "recente") {
      // For slots, try to align with the first child element (AppIndexTitle)
      const recenteTitleElement = targetElement.firstElementChild;
      if (recenteTitleElement && headerRect) {
        const recenteTitleRect = recenteTitleElement.getBoundingClientRect();
        console.log(
          "index.vue: Found first child for slots. Calculating based on header bottom and title top."
        );
        scrollToPosition =
          scrollTop + recenteTitleRect.top - headerRect.bottom + extraHeight;
        console.log(
          `index.vue: slots - scrollTop: ${scrollTop}, recenteTitleRect.top: ${recenteTitleRect.top}, headerRect.bottom: ${headerRect.bottom}`
        );
      } else {
        console.warn(
          "index.vue: Could not find first child element or header element for slots. Falling back."
        );
        const rect = targetElement.getBoundingClientRect();
        scrollToPosition = rect.top + scrollTop - headerHeight;
      }
    } else {
      // Fallback for other sections
      console.log(
        `index.vue: Calculating for section ${sectionId} using fallback (target top - header height).`
      );
      const rect = targetElement.getBoundingClientRect();
      scrollToPosition = rect.top + scrollTop - headerHeight;
    }

    // Final logging before scrolling
    console.log(
      `index.vue: Final scrollToPosition for ${sectionId}:`,
      scrollToPosition
    );

    // Use scrollContainer.scrollTo
    scrollContainer.scrollTo({
      top: scrollToPosition,
      behavior: "smooth",
    });
  } else {
    // 如果未成功滚动
    if (!targetElement)
      console.error(
        "Could not find target DOM element for section:",
        sectionId
      );
    if (!scrollContainer) console.error("Could not find scroll container."); // Log if container not found
  }
};

// 暴露方法供父组件调用
defineExpose({
  scrollToSection,
});
</script>

<template>
  <div class="app-index-layout-container">
    <div class="left-menu">
      <AppGameTab scrollIconId="scrollIconId" />
    </div>
    <div class="right-content">
      <!-- <div class="g-filter">
        <AppGameFilterGlobal :id="+GameNavEnum.Quente" :setFilterGameParam="setFilterGameParam" />
      </div> -->
      <!-- <section class="section" id="setting">
        <AppIndexGameContainer
          :id="GameNavEnum.Quente"
          class="game-container-list"
          :filter-type="filterType"
          ref="popularGamesRef"
        />
      </section>
      <section class="section" id="setting1">
        <AppGameRecente class="game-container-list" ref="recenteGamesRef" />
      </section> -->

      <AppTabGameContainer
        :id="GameNavEnum.Quente"
        class="game-container-list"
        :filter-type="filterType"
        ref="popularGamesRef"
      />
      <!-- 原AppIndexRecFavGameContainer因已集成到AppTabGameContainer中，可以移除 -->
      <!-- <AppIndexRecFavGameContainer
        :id="GameNavEnum.Quente"
      /> -->
      <!-- 热门游戏-->
      <!--<AppGamePlatform />   三方平台-->
      <AppIndexSlotsContainer
        :id="GameNavEnum.Slots"
        class="game-container-list"
        :filter-type="filterType"
        v-if="!isSmallGameIcon"
        ref="slotsGamesRef"
      />
      <!--  老虎机分类-->
      <!-- <AppIndexGameContainer :id="GameNavEnum.Dentro_De_Casa" class="game-container-list" :filter-type="filterType" /> -->
      <AppSingleSlotsContainer
        :id="GameNavEnum.Slots"
        class="game-container-list"
        :filter-type="filterType"
        v-else
        ref="slotsGamesRef"
      />

      <AppBlockchainContainer
        :id="GameNavEnum.Blockchain"
        class="game-container-list"
        :filter-type="filterType"
        ref="BlockchainGamesRef"
      />

      <AppAoVivoContainer
        :id="GameNavEnum.AoVivo"
        class="game-container-list"
        :filter-type="filterType"
        ref="AoVivoGamesRef"
      />

      <AppPescariaContainer
        :id="GameNavEnum.Pescaria"
        class="game-container-list"
        :filter-type="filterType"
        ref="PescariaGamesRef"
      />

      <!--  一行一列广告图-->
      <!-- <AppGameRecente class="game-container-list" ref="recenteGamesRef" /> -->

      <!-- 写死 pg-->
      <!--捕鱼 视讯 binggo 接机-->
      <!-- <template v-for="item in GameSoltTypeEnum">  
        <AppIndexGameContainer v-if="typeof(item) == 'string' " :id="GameNavEnum.Slot" :platform_id="`${GameSoltTypeEnum[item]}`"  class="game-container-list" :filter-type="filterType" fav-type="rec" />
      </template> -->
      <!--捕鱼 视讯 binggo 接机-->
      <!-- <template v-for="nav in gameNavData">  
        <AppIndexGameContainer v-if="gameNavInit[nav.id] && gameNavInit[nav.id].length && nav.id !=GameNavEnum.Slot " :id="nav.id"
          class="game-container-list" :filter-type="filterType" fav-type="rec" />
      </template> -->
      <!-- <AppIndexLatestWin class="index-latest-win" /> -->

      <!-- <div class="copyright">
        Copyright © All Rights Reserved by boabet555
      </div> -->
    </div>
  </div>
  <div class="app-index-bottom">
    <div class="app-index-bottom-btn">
      <div class="leftBtn">
        <label class="title">Cassino</label>
        <label class="titleBtn">Rebate</label>
        <label class="titleBtn">VIP</label>
        <label class="titleBtn">Convidar</label>
        <label class="titleBtn">Eventos</label>
        <label class="titleBtn">Bônus</label>
        <label class="titleBtn">Missão</label>
      </div>

      <div class="centerBtn">
        <label class="title">Jogos</label>
        <label class="titleBtn">Brigas Galos</label>
        <label class="titleBtn">Cartas</label>
        <label class="titleBtn">Pescaria</label>
        <label class="titleBtn">Slots</label>
        <label class="titleBtn">Ao Vivo</label>
        <label class="titleBtn">Esporte</label>
        <label class="titleBtn">Loteria</label>
      </div>

      <div class="rightBtn">
        <label class="title">Suporte</label>
        <label class="titleBtn">Suporte</label>
        <label class="titleBtn">Central de Ajuda</label>
        <label class="titleBtn">Bônus de Sugestão</label>
      </div>
    </div>

    <div class="app-index-bottom-line"></div>
    <div class="app-index-bottom-fb">
      <div v-for="n in 8" class="app-index-bottom-item">
        <AppImage class="img" :src="`/icons/bottom_btn_${n}`" alt="" />
      </div>
    </div>
    <div class="statement-content">
      O grupo
      <span style="font-size: 12px"><strong> boabet555 </strong></span> é uma
      das mais renomadas empresas internacionais de operação de cassino online,
      oferecendo uma ampla variedade de jogos empolgantes, como jogos ao vivo
      com crupiê real, slots, pesca, loterias, esportes e muito mais. Estamos
      autorizados e regulamentados pelo governo de Curaçao, operando com a
      licença número
      <span
        style="
          font-family: 'Helvetica Neue', Helvetica, 'PingFang SC',
            'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
        "
        >Antillephone</span
      >
      emitida para a 8048/JAZ. Passamos por todas as verificaçõe
    </div>
    <div class="app-index-bottom-line"></div>

    <div class="app-index-bottom-fb">
      <div class="app-index-bottom-item">
        <AppImage class="img2" src="/icons/index_menu/icon_footer_pg" alt="" />
        <AppImage class="img2" src="/icons/index_menu/icon_footer_jdb" alt="" />
        <AppImage
          class="img2"
          src="/icons/index_menu/icon_footer_ppgame"
          alt=""
        />
        <AppImage
          class="img2"
          src="/icons/index_menu/icon_footer_mgame"
          alt=""
        />
        <AppImage class="img2" src="/icons/index_menu/icon_footer_wg" alt="" />
      </div>
    </div>
    <div class="Direitos">@ Direitos De Autor 2002-2025</div>

    <!-- <AppImage class="img" src="/icons/logo.png" alt="" /> -->
    <!-- <div class="textTitle">2024 boabet555.com All rights reserved.</div> -->
    <!-- <div class="textMsg">
          As informações sobre handicap e probabilidades esportivas contidas neste
          site são apenas para fins de entretenimento. Favor verificar os regulamentos
          de apostas em sua jurisdição, pois eles variam de estado para estado, de
          província para província e de país para pais. É proibido usar estas informações
          para violar qualquer lei ou estatuto. O site não está associado a nenhuma liga,
          associação ou equipe profissional ou colegiada, nem é endossado por ela.
          Esse site não tem como alvo público menores de 18 anos de idade.
        </div> -->
    <!-- <br> -->
    <!-- <div class="button-one">
          <AppButton font-size="24" color="#FFFFFF" blue3 radius="10" width="226" height="58" @click="gotoProtocal(0)">Terms of Service</AppButton>
          <AppButton font-size="24" color="#FFFFFF" blue3 radius="10" width="161" height="58" @click="gotoProtocal(1)">KYC Policy</AppButton>
        </div>
        <div class="button-one">
          <AppButton font-size="24" color="#FFFFFF" blue3 radius="10" width="200" height="58" @click="gotoProtocal(2)">Privacy Policy</AppButton>
          <AppButton font-size="24" color="#FFFFFF" blue3 radius="10" width="281" height="58" @click="gotoProtocal(3)">Responsible Gambling</AppButton>
        </div>
        <div class="index-container1">
          <AppImage class="jobs-img" src="/img/share.png" alt="" />
        </div>
        <div class="index-container">
          <AppImage class="jobs-img" src="/img/jobs.png" alt="" />
        </div>
        <div class="index-container2">
          <AppImage class="jobs-img" src="/img/bottom.png" alt="" />
        </div> -->
  </div>
</template>

<style lang="scss" scoped>
/* 移除之前的html样式 */
/* html {
  scroll-behavior: smooth;
} */

.app-index-layout-container {
  position: relative;
  overflow-x: visible !important;
  display: flex;
  /* 添加这一行 */
  height: 100%;
  .left-menu {
    width: 130px;
    margin-left: -8px;
    flex-shrink: 0;
    z-index: 1;
    /* 确保left-menu不限制子元素的sticky效果 */
    overflow: visible;
    position: static;
  }

  .right-content {
    flex: 1;
    // padding-left: 130px;
    overflow-x: hidden;
    padding-left: 45px;
    padding-right: 10px;
    box-sizing: border-box;
    /* scroll-behavior: smooth; */ /* 移除平滑滚动 */
  }

  .g-filter {
    position: absolute;
    right: 0;
    top: 28px;
  }
}

.game-container-list {
  // padding-top: 30px;
}

.app-index-bottom {
  // width: 100%;
  padding: 20px;
  // border-radius: 20px;
  margin-top: 30px;
  margin-left: -20px;
  margin-right: -20px;
  padding-bottom: var(--app-footer-height);
  background-color: var(--theme-btm-bg-color);
  .app-index-bottom-btn {
    display: flex;
  }

  .leftBtn {
    width: 33%;
    // height: 300px;
    // background-color: #3e7832;
  }
  .centerBtn {
    width: 33%;
    // height: 300px;
    // background-color: #3a1212;
  }
  .rightBtn {
    width: 34%;
    // height: 300px;
    // background-color: #000000;
  }
  .title {
    display: block;
    color: var(--theme-text-color);
    font-size: 26px;
    margin-bottom: 15px;
  }
  .titleBtn {
    display: block;
    color: var(--theme-text-color-lighten);
    font-size: 26px;
    margin-bottom: 10px;
  }

  .app-index-bottom-line {
    width: 100%;
    height: 1px;
    background-color: var(--theme-color-line);
    margin-top: 35px;
    margin-bottom: 10px;
  }

  .app-index-bottom-fb {
    display: flex;
    margin-top: 50px;
  }

  .app-index-bottom-item {
    flex: 1 1 0%;
  }

  .img {
    width: 64px;
    display: inline-block;
    margin-left: 7px;
  }

  .statement-content {
    margin-top: 50px;
    text-align: center;
    color: var(--theme-text-color);
    font-size: 20px;
    word-break: break-all;
  }

  .img2 {
    width: 112px;
    display: inline-block;
    margin-left: 16px;
  }

  .Direitos {
    margin-top: 40px;
    margin-bottom: 5vw;
    color: var(--theme-text-color);
    font-size: 24px;
    text-align: center;
  }
  // .text{
  //   font-weight: normal;
  //   font-size: 19px;
  //   line-height: 26px;
  //   margin-bottom: 30px;
  //   color: #333;
  // }

  // .textTitle{
  //   color:var(--app-desc-color);
  //   font-size:30px;
  //   line-height: 70px;
  // }

  // .textMsg{
  //   font-size:24px;
  //   line-height:40px;
  //   font-family: inherit;
  //   white-space: pre-wrap;
  //   color:var(--app-desc-color);
  // }

  // .button-one{
  //   width: 100%;
  //   margin-bottom: 15px;
  //   button{
  //     display: inline-block;
  //     margin-right: 15px;
  //   }
  // }

  // .index-container{
  //   margin: 15px;
  //   margin-top: 30px;
  // }
  // .index-container1{
  //   margin: 30px;
  //   margin-top: 50px;
  // }
  // .index-container2{
  //   margin: 60px;
  //   margin-top: 50px;
  //   margin-bottom: 20px;
  // }
}

.index-latest-win {
  margin-top: 80px;
}

.index-title {
  color: #fff;
  font-size: 32px;
  font-weight: 700;
  padding-top: 30px;
  padding-bottom: 27px;
}

.jobs-img {
  width: 100%;
}

.index-line {
  position: relative;
  width: 100%;
  margin-top: 39px;

  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 1px dashed #828eb4;
    pointer-events: none;
    transform: scaleY(0.5);
    transform-origin: top left;
  }
}

.index-b-img {
  vertical-align: middle;
}

.index-b-img-lx {
  width: 76px;

  &:not(:last-child) {
    margin-right: 30px;
  }
}

.index-b-img-139 {
  width: 139px;
}

.index-b-img-92 {
  width: 92px;
}

.index-title-2 {
  color: rgba(255, 255, 255, 0.6);
  font-size: 26px;
  padding-top: 40px;
}

.bottom-link {
  color: #ccced2;
  text-align: center;
  font-size: 22px;
  display: flex;
  justify-content: space-around;
  padding: 35px 0 50px 0;

  .link-img {
    height: 51px;
  }
}

.copyright {
  color: rgba(255, 255, 255, 0.6);
  text-align: center;
  font-size: 24px;
  height: 94px;
  background: #28374d;
  line-height: 94px;
}
</style>

<!-- 添加全局样式 -->
<style></style>

<route lang="yaml">
meta:
  layout: home
</route>
