<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg width="100%" height="100%" viewBox="0 0 80 80" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" xmlns:serif="http://www.serif.com/" style="fill-rule:evenodd;clip-rule:evenodd;stroke-linejoin:round;stroke-miterlimit:2;">
    <g>
        <g>
            <g transform="matrix(1.00519,0,0,1.01744,-10.973,-5.42689)">
                <circle cx="51.079" cy="48.824" r="12.764" style="fill:var(--svg-icon-color);"/>
            </g>
            <use xlink:href="#_Image1" x="15.121" y="38.581" width="60px" height="40px"/>
            <path d="M67.233,44.629C68.507,44.629 69.54,45.662 69.54,46.937C69.54,52.156 69.54,64.817 69.54,70.036C69.54,71.311 68.507,72.344 67.233,72.344C59.073,72.344 31.636,72.344 23.477,72.344C22.202,72.344 21.169,71.311 21.169,70.036C21.169,64.817 21.169,52.156 21.169,46.937C21.169,45.662 22.202,44.629 23.477,44.629C31.636,44.629 59.073,44.629 67.233,44.629Z" style="fill:var(--svg-icon-color3);"/>
            <use xlink:href="#_Image2" x="9.085" y="14.289" width="23px" height="23px"/>
        </g>
        <g transform="matrix(0.965926,0.258819,-0.258819,0.965926,5.13725,-7.30881)">
            <circle cx="23.296" cy="27.036" r="9.298" style="fill:var(--svg-icon-color);"/>
        </g>
        <g transform="matrix(-0.808496,0.808496,-0.698709,-0.698709,110.164,-13.03)">
            <path d="M56.256,10.202C57.056,13.473 58.239,18.791 58.239,21.615C58.239,24.804 56.001,27.393 53.246,27.393C50.49,27.393 48.253,24.804 48.253,21.615C48.253,18.826 51.369,13.606 53.526,10.327C50.24,8.733 45.309,6.023 44.107,3.614C42.73,0.852 43.549,-2.684 45.935,-4.279C48.322,-5.873 51.378,-4.925 52.756,-2.164C54.16,0.65 55.634,7.228 56.256,10.202ZM54.686,12.493C53.666,14.079 52.511,16 51.616,17.833C50.907,19.285 50.308,20.627 50.308,21.615C50.308,23.491 51.624,25.015 53.246,25.015C54.867,25.015 56.183,23.491 56.183,21.615C56.183,19.384 55.395,15.518 54.686,12.493ZM53.621,7.792C52.927,4.762 51.94,0.958 50.976,-0.975C50.165,-2.599 48.367,-3.157 46.963,-2.219C45.559,-1.281 45.077,0.8 45.887,2.425C46.314,3.28 47.413,4.096 48.655,4.943C50.222,6.013 52.052,7.008 53.621,7.792Z" style="fill:var(--svg-icon-color);"/>
        </g>
        <g transform="matrix(0.821313,-0.474185,0.474185,0.821313,-15.6599,-14.9825)">
            <path d="M11.629,63.32L11.629,61.804C10.871,61.708 10.255,61.538 9.78,61.294C9.305,61.05 8.895,60.655 8.55,60.11C8.204,59.566 8.004,58.9 7.948,58.114L9.472,57.827C9.59,58.642 9.798,59.24 10.096,59.622C10.523,60.161 11.034,60.461 11.629,60.523L11.629,55.696C11.006,55.578 10.368,55.336 9.717,54.971C9.234,54.701 8.862,54.328 8.6,53.851C8.339,53.373 8.209,52.831 8.209,52.224C8.209,51.146 8.591,50.273 9.355,49.604C9.866,49.155 10.624,48.88 11.629,48.779L11.629,48.054L12.522,48.054L12.522,48.779C13.404,48.863 14.103,49.121 14.62,49.554C15.283,50.104 15.682,50.86 15.817,51.82L14.25,52.056C14.16,51.461 13.973,51.004 13.689,50.687C13.406,50.37 13.017,50.16 12.522,50.059L12.522,54.432C13.286,54.623 13.792,54.772 14.039,54.878C14.511,55.086 14.895,55.339 15.193,55.637C15.491,55.934 15.72,56.288 15.88,56.698C16.04,57.108 16.12,57.552 16.12,58.029C16.12,59.08 15.786,59.956 15.117,60.658C14.449,61.36 13.584,61.736 12.522,61.787L12.522,63.32L11.629,63.32ZM11.629,50.042C11.04,50.132 10.575,50.368 10.235,50.75C9.895,51.132 9.725,51.584 9.725,52.106C9.725,52.623 9.87,53.056 10.159,53.404C10.448,53.752 10.938,54.03 11.629,54.238L11.629,50.042ZM12.522,60.523C13.112,60.45 13.599,60.195 13.984,59.757C14.369,59.318 14.561,58.776 14.561,58.13C14.561,57.58 14.425,57.138 14.153,56.804C13.88,56.469 13.337,56.17 12.522,55.906L12.522,60.523Z" style="fill:var(--svg-icon-color3);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1.14865,0.30778,-0.28672,1.07005,42.4119,-20.0916)">
            <path d="M11.629,63.32L11.629,61.804C10.871,61.708 10.255,61.538 9.78,61.294C9.305,61.05 8.895,60.655 8.55,60.11C8.204,59.566 8.004,58.9 7.948,58.114L9.472,57.827C9.59,58.642 9.798,59.24 10.096,59.622C10.523,60.161 11.034,60.461 11.629,60.523L11.629,55.696C11.006,55.578 10.368,55.336 9.717,54.971C9.234,54.701 8.862,54.328 8.6,53.851C8.339,53.373 8.209,52.831 8.209,52.224C8.209,51.146 8.591,50.273 9.355,49.604C9.866,49.155 10.624,48.88 11.629,48.779L11.629,48.054L12.522,48.054L12.522,48.779C13.404,48.863 14.103,49.121 14.62,49.554C15.283,50.104 15.682,50.86 15.817,51.82L14.25,52.056C14.16,51.461 13.973,51.004 13.689,50.687C13.406,50.37 13.017,50.16 12.522,50.059L12.522,54.432C13.286,54.623 13.792,54.772 14.039,54.878C14.511,55.086 14.895,55.339 15.193,55.637C15.491,55.934 15.72,56.288 15.88,56.698C16.04,57.108 16.12,57.552 16.12,58.029C16.12,59.08 15.786,59.956 15.117,60.658C14.449,61.36 13.584,61.736 12.522,61.787L12.522,63.32L11.629,63.32ZM11.629,50.042C11.04,50.132 10.575,50.368 10.235,50.75C9.895,51.132 9.725,51.584 9.725,52.106C9.725,52.623 9.87,53.056 10.159,53.404C10.448,53.752 10.938,54.03 11.629,54.238L11.629,50.042ZM12.522,60.523C13.112,60.45 13.599,60.195 13.984,59.757C14.369,59.318 14.561,58.776 14.561,58.13C14.561,57.58 14.425,57.138 14.153,56.804C13.88,56.469 13.337,56.17 12.522,55.906L12.522,60.523Z" style="fill:var(--svg-icon-color3);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(0.961452,0.555095,-0.467258,0.809314,17.6974,-11.3741)">
            <path d="M68.459,12.228C69.177,12.228 69.866,12.567 70.374,13.171C70.882,13.774 71.168,14.593 71.168,15.446C71.168,17.125 71.168,19.172 71.168,20.85C71.168,21.704 70.882,22.522 70.374,23.126C69.866,23.729 69.177,24.068 68.459,24.068C60.173,24.068 35.224,24.068 26.938,24.068C26.22,24.068 25.531,23.729 25.023,23.126C24.515,22.522 24.229,21.704 24.229,20.85C24.229,19.172 24.229,17.125 24.229,15.446C24.229,14.593 24.515,13.774 25.023,13.171C25.531,12.567 26.22,12.228 26.938,12.228C35.224,12.228 60.173,12.228 68.459,12.228Z" style="fill:var(--svg-icon-color3);"/>
        </g>
        <g transform="matrix(1,0,0,1.06361,1.68909,-3.49038)">
            <path d="M46.423,45.242L46.423,71.299L40.909,71.299L40.909,45.242L46.423,45.242ZM59.064,28.21L53.46,37.336L48.685,34.744L54.289,25.617L59.064,28.21Z" style="fill:var(--svg-icon-color);"/>
        </g>
    </g>
    <defs>
        <image id="_Image1" width="60px" height="40px" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADwAAAAoCAYAAACiu5n/AAAACXBIWXMAAA7EAAAOxAGVKw4bAAACUklEQVRoge2a3XKjMAxGD4SG9Gff/0l3GxIIvbC/WFZCk150RsvwzWhsCnR8kGwciYbn1Dx5XQTN351cAmnMOd9G1fygBaBzNzXO2jt/iygLd8mt+vZ8BWAhW2BnzINHkwWcTKu+HkLlYcHugBdjXTY9iGjAHnYEzsbGfN0FmAVsvfsC7IFDtp4CHhl4IgGegAE4UsZ6zv3Ze1jePQDvwBvwSoHeERNY3j2RQP+RASmhPQON9bAN5wMJ9g/wkY/31MARwLUYKZQHCuyFEt6ay/iQFnBP8uwHCfqNAtz+PsePJC+OJO+2JLgz6QEMlJCmo/aY9XJP8uwrsYGhzN829wfSeDtKVDaYkIYauKMsXr1pIwLLwwrjPWWRta9UoA5ptRZc1lG/kyNppixQfu9g9w/A7XsYc8G9XVfE15Jgl8aN7T/yVtSd1ZKWYK+KFp6/rg147dqA164NeO3agNeuDXjt2oDXrkfAygj6fiTNrm8N169+D/uLL858Nj+SlsbqH8YV2JcqJmNjth3lh3Y0aZyT61dVB7jvYZvePGXr8jmb9IsijdlnKS301dsC0Y22VDEAnxTPnomb0xLwkTTmIwX8mpPGVB7snNWNqjQIVsdRgW0i/i+J4UTt5WoO2/rMQIIjH/fc5ngjSNFpp6FKLZ8k4JGF6qEFViZQT816NyqwolMOU1hb4JuQxh0LWLneSLBWArJrkK8r3XgYc5P+yUjJR2vuRgT2e4fJtddV2g/e15ke5nmDyO+q/Obj7icPVv/bBy3Stx+0wM9AokM/tc//AvBVButE+lSlAAAAAElFTkSuQmCC"/>
        <image id="_Image2" width="23px" height="23px" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABcAAAAXCAYAAADgKtSgAAAACXBIWXMAAA7EAAAOxAGVKw4bAAAB3klEQVRIiZ2Ve2/aQBDEfxhDKI+kKK3afP8vF+WhECgxwdj0D8/g9WEkkpVWd37czN7O3t6AfhsAGTAMnun9EagSP8rPQFLLgBwYAxPgRp7r/xoogU9gp7EEDinBIJlnAp0CC/kc+AGM9E8F7IEPYCP/J6JS5KBoYsQTgS2BX8BPEUySyA2+Bt6AV2AFbCNBHoDHAv4N/NF4F6KOOT+I4BaYaW2mbyY4OppcqVgK+EHzqb4N6dqYVgsT1yKtPM9D1AvgXhEvtQt/T21Aq5F3UyrvhXZVDcU8E/BfgS9CRJfMBEO6WhQiObiWLaQr41LEfQTe+UxBTfWcGXws0CjetebozzAy2kMzChH3Ha5rCDo4Bkr9u9bBcG3WtCV01iOutDOcTA8ljdJ72kb0FXONG+MA1Ab/pDnOheZfITDwjuZ0us5P4DuaBrTWD3uuS5Hb746mea017oGjj7Xr1a3WivuA9Ins/BbAO/AMPNE0sgKd0BgFtJdEWpL+btBSICsBPwIvfZF7cR0Wp88VrWjO71sCvKbRrNNyfbtsaVuqdXA/92XhHrIV2EokG605FUOaS+f9hqZHzDX23USFUhArpFNlfUJZ3JE8CuzIY03H+/PiHXqJxO6qiVq4XHtL9j8KHb7Pyj/VMgAAAABJRU5ErkJggg=="/>
    </defs>
</svg>
