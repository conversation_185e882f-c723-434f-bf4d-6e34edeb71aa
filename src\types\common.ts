export enum GameNavEnum {
  Ao_Vivo = 1, //视讯
  Pesca = 2, //捕鱼
  Slot = 3, //老虎机的 game_type 就是各种老虎机
  Esporte = 4,
  Pôquer = 5,
  All = 0,
  Bingo = 7, //bingo
  // Jo<PERSON>_de_video = 8,  //街机

  Quente = 100, //热门
  Dentro_De_Casa = 101,
  Slots = 600, //直接写死几张图片
  Platform = 1000, //直接写死几张图片
  Blockchain= 2501,
  AoVivo= 2502,
  Pescaria=2503,
}

//子节点
export enum GameSoltTypeEnum {
  Slot_pg = 101, //三方老虎机的平台
  Slot_pp = 201,
  Slot_jdb = 503,
  Slot_jili  = 603,
  Slot_fc = 703,
  Slot_yesbingo = 803,
  // Slot_haba  = 903,
  // Slot_hacksaw  = 1003,
  Slot_tada = 1103,
}
export type GameItem = {
  game_type: number;
  id: string;
  maintained: number;
  name: string;
  seq: number;
  state: number;
  key: number;
  [k: string]: any;
};

//三方平台 父节点
export enum GameTypeEnum {
  platform_pg = "101", //三方老虎机的平台
  platform_pp = "201,301",
  platform_evo = "401",
  platform_jdb = "501,503",
  platform_jili = "603", //目前没用
  platform_fc = "703",
  platform_yesbingo = "801,803",
  platform_haba = "903",
  platform_hacksaw = "1003",
  platform_tada = "1102,1103",

  // platform_pg = 1,      //三方老虎机的平台
  // platform_pp = 2,
  // platform_evo = 3,
  // platform_jdb  = 4,
  // platform_jili  = 5, //目前没用
  // platform_fc = 6,
  // platform_yesbingo =7,
  // platform_haba  = 8,
  // platform_hacksaw  = 9,
  // platform_tada  = 10,
}

export enum CycleModeEnum {
  Sequential_Loop = 1, //顺序循环
  Random_Cycle = 2, //随机循环
  Single_Loop = 3, //单曲循环
}

export enum GameHallTopEnum {
  Popular = 10000,
  Solts = 10001,
  Recente = 10002,
  Favoritos = 10003,
}
export enum GameNewHallTopEnum {
  Popular = 10000,
  Slots = 10001,
  Recente = 10002,
  Favoritos = 10003,
  Blockchain = 10004,
  AoVivo = 10005,
  Pescaria = 10006,
}

export enum PGSlotsImgTypeEnum {
  w1 = 0,
  fa,
  fp,
  kf,
  r7,
}
