<script setup lang='ts' name='relatorio'>


const router = useRouter()
const appStore = useAppStore()
const { isApp } = storeToRefs(appStore)
const showPopover = ref(false);
const showPopover1 = ref(false);
const showPopover2 = ref(false);
const selectIndex = ref(2);
const selectIndex1 = ref(2);
const selectIndex2 = ref(2);
const selText = ref("Hoje")
const selText1 = ref("Todos os Tipos")
const selText2 = ref("Plataforms")
let actions = [{ text: "Hoje", calssName: "2", color: selectIndex.value == 2 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
{ text: "Ontem", calssName: "1", color: selectIndex.value == 1 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
{ text: "Últimos 7 Dias", calssName: "3", color: selectIndex.value == 3 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
{ text: "Últimos 15 Dias", calssName: "4", color: selectIndex.value == 4 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
{ text: "Últimos 30 Dias", calssName: "5", color: selectIndex.value == 5 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" }]

let actions1 = [{ text: "Todos os Tipos", calssName: "2", color: selectIndex.value == 2 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
{ text: "Slot", calssName: "1", color: selectIndex.value == 1 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" }]


let actions2 = [{ text: "Plataforms", calssName: "2", color: selectIndex.value == 2 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" }]


const popoverStatus = (isOpen: boolean) => {
    if (!isOpen) {
        actions = [{ text: "Hoje", calssName: "2", color: selectIndex.value == 2 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
        { text: "Ontem", calssName: "1", color: selectIndex.value == 1 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
        { text: "Últimos 7 Dias", calssName: "3", color: selectIndex.value == 3 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
        { text: "Últimos 15 Dias", calssName: "4", color: selectIndex.value == 4 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
        { text: "Últimos 30 Dias", calssName: "5", color: selectIndex.value == 5 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" }]
    }
}

const popoverStatus1 = (isOpen: boolean) => {
    if (!isOpen) {
        actions1 = [{ text: "Todos os Tipos", calssName: "2", color: selectIndex.value == 2 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" },
        { text: "Slot", calssName: "1", color: selectIndex.value == 1 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" }]

    }
}


const popoverStatus2 = (isOpen: boolean) => {
    if (!isOpen) {
        actions2 = [{ text: "Plataforms", calssName: "2", color: selectIndex.value == 2 ? "var(--theme-primary-color)" : "var(--theme-text-color-lighten)" }]
    }
}


const onSelect = (action: any) => {
    // console.log("onSelect action: " + JSON.stringify(action))
    //showToast(action.text);
    selText.value = action.text;
    selectIndex.value = Number(action.calssName)
    //runUserWitdrawList();
}




const onSelect1 = (action: any) => {
    // console.log("onSelect action: " + JSON.stringify(action))
    //showToast(action.text);
    selText1.value = action.text;
    selectIndex1.value = Number(action.calssName)
    //runUserWitdrawList();
}

const onSelect2 = (action: any) => {
    // console.log("onSelect action: " + JSON.stringify(action))
    //showToast(action.text);
    selText2.value = action.text;
    selectIndex2.value = Number(action.calssName)
    //runUserWitdrawList();
}


// <AppImage src="/img/finance/finance-icon.png" class="list-item-conetent-top-left-img" />
</script>
<template>
    <div class="conta">
        <div class="page-tab-panel">
            <section class="page-tab-panel-content">
                <div class="panel-content-header">
                    <van-popover class="content-header-popover" v-model:show="showPopover" :actions="actions"
                        @select="onSelect" @open="popoverStatus(true)" @close="popoverStatus(false)"
                        placement="bottom-start" :show-arrow=false>
                        <template #reference>
                            <div class="content-header-popover-select box-border" :class="{ 'viewOpen': showPopover }">
                                <van-text-ellipsis :content="selText" class="content-header-select-title" />
                                <span class="content-header-select-icon"
                                    :class="{ 'rotate-svg': showPopover, 'rotate-svg1': !showPopover }">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em"
                                        viewBox="0 0 35.969 21.998" data-icon="down" fill="currentColor"
                                        aria-hidden="true" focusable="false" class="">
                                        <path id="comm_icon_fh"
                                            d="M2209.28,137.564l-17.743-15.773a2,2,0,0,1-.271-3.058,2.037,2.037,0,0,1,.274-.23l17.74-15.77a1.992,1.992,0,0,1,2.817,2.816l-16.422,14.6,16.422,14.6a1.992,1.992,0,1,1-2.817,2.817Z"
                                            transform="translate(-102.181 2212.68) rotate(-90)"></path>
                                    </svg>
                                </span>
                            </div>
                        </template>
                    </van-popover>
                    <van-popover class="content-header-popover" v-model:show="showPopover1" :actions="actions1"
                        @select="onSelect1" @open="popoverStatus1(true)" @close="popoverStatus1(false)"
                        placement="bottom-start" :show-arrow=false>
                        <template #reference>
                            <div class="content-header-popover-select1 box-border"
                                :class="{ 'viewOpen': showPopover1 }">
                                <van-text-ellipsis :content="selText1" class="content-header-select-title1" />
                                <span class="content-header-select-icon"
                                    :class="{ 'rotate-svg': showPopover1, 'rotate-svg1': !showPopover1 }">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em"
                                        viewBox="0 0 35.969 21.998" data-icon="down" fill="currentColor"
                                        aria-hidden="true" focusable="false" class="">
                                        <path id="comm_icon_fh"
                                            d="M2209.28,137.564l-17.743-15.773a2,2,0,0,1-.271-3.058,2.037,2.037,0,0,1,.274-.23l17.74-15.77a1.992,1.992,0,0,1,2.817,2.816l-16.422,14.6,16.422,14.6a1.992,1.992,0,1,1-2.817,2.817Z"
                                            transform="translate(-102.181 2212.68) rotate(-90)"></path>
                                    </svg>
                                </span>
                            </div>
                        </template>

                    </van-popover>
                    <van-popover class="content-header-popover" v-model:show="showPopover2" :actions="actions2"
                        @select="onSelect2" @open="popoverStatus2(true)" @close="popoverStatus2(false)"
                        placement="bottom-start" :show-arrow=false>
                        <template #reference>
                            <div class="content-header-popover-select2 box-border"
                                :class="{ 'viewOpen': showPopover2 }">
                                <van-text-ellipsis :content="selText2" class="content-header-select-title2" />
                                <span class="content-header-select-icon"
                                    :class="{ 'rotate-svg': showPopover2, 'rotate-svg1': !showPopover2 }">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em"
                                        viewBox="0 0 35.969 21.998" data-icon="down" fill="currentColor"
                                        aria-hidden="true" focusable="false" class="">
                                        <path id="comm_icon_fh"
                                            d="M2209.28,137.564l-17.743-15.773a2,2,0,0,1-.271-3.058,2.037,2.037,0,0,1,.274-.23l17.74-15.77a1.992,1.992,0,0,1,2.817,2.816l-16.422,14.6,16.422,14.6a1.992,1.992,0,1,1-2.817,2.817Z"
                                            transform="translate(-102.181 2212.68) rotate(-90)"></path>
                                    </svg>
                                </span>
                            </div>
                        </template>

                    </van-popover>
                </div>
                <div class="content-body-rightup">
                    Apostas/<br>Valor/<br>Ganhos
                </div>

                <div class="content-body-bottom">
                    <div class="content-body-bottom-a">Número total de apostas efetua...</div>
                    <div class="content-body-bottom-b">{{ '0' }}</div>
                    <div class="content-body-bottom-c">Total de Apostas Validas</div>
                    <div class="content-body-bottom-d">{{ 'R$0.00' }}</div>
                    <div class="content-body-bottom-e">Total de V/D</div>
                    <div class="content-body-bottom-f">{{ 'R$0.00' }}</div>
                </div>
            </section>
        </div>
    </div>
    <div class="content-body-middle">
                    <app-empty />
                </div>
</template>

<style lang='scss' scoped>
.conta {
    height: 400px;
    .conta-content {
        width: 100%;
        position: relative;
        height: 200px;
    }

    .page-tab-panel {
        display: block;
        width: 100%;
        height: 100%;
        color: var(--theme-text-color);
        font-size: 14px;
        //background-color: aqua;

        .page-tab-panel-content {
            height: 100%;
            padding: 0 20px 20px;
        }

        .panel-content-header {
            position: absolute;
            width: 600px;
            height: 90px;
            overflow: hidden;
            //padding: 10px 0 0 0;
            color: var(--theme-text-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 22px;
        }

        .content-header-popover {
            width: 160px;
            height: 50px;
        }

        .content-header-popover1 {
           // --van-popover-action-width: 300px;
        }


        .content-header-popover-select {
            width: 160px;
            height: 50px;
            border-radius: 25px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
            background-color: var(--theme-main-bg-color);

            &.viewOpen {
                border-color: var(--theme-primary-color);
            }
        }

        .content-header-popover-select1 {
            width: 200px;
            height: 50px;
            border-radius: 25px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
            //left: -100px;
            background-color: var(--theme-main-bg-color);

            &.viewOpen {
                border-color: var(--theme-primary-color);
            }
        }

        .content-header-popover-select2 {
            width: 200px;
            height: 50px;
            border-radius: 25px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
            //left: -100px;
            background-color: var(--theme-main-bg-color);

            &.viewOpen {
                border-color: var(--theme-primary-color);
            }
        }

        .content-header-select-title {
            color: var(--theme-text-color);
            font-size: 20px;
            line-height: 48px;
            padding-left: 20px;
            width: 101px;
            height: 48px;
        }

        .content-header-select-title1 {
            color: var(--theme-text-color);
            font-size: 20px;
            line-height: 48px;
            padding-left: 20px;
            width: 150px;
            height: 48px;
        }

        .content-header-select-title2 {
            color: var(--theme-text-color);
            font-size: 20px;
            line-height: 48px;
            padding-left: 20px;
            width: 251px;
            height: 48px;
        }

        @-webkit-keyframes spin0 {
            from {
                -webkit-transform: rotate(0deg);
            }

            to {
                -webkit-transform: rotate(180deg);
            }
        }

        @keyframes spin0 {
            from {
                transform: rotate(0deg);
            }

            to {
                transform: rotate(180deg);
            }
        }

        @-webkit-keyframes spin1 {
            from {
                -webkit-transform: rotate(180deg);
            }

            to {
                -webkit-transform: rotate(0deg);
            }
        }

        @keyframes spin1 {
            from {
                transform: rotate(180deg);
            }

            to {
                transform: rotate(0deg);
            }
        }

        .rotate-svg {
            animation: spin0 0.3s linear 0s 1;
            transform: rotate(180deg);
        }

        .rotate-svg1 {
            transform: rotate(180deg);
            animation: spin1 0.3s linear 0s 1;
            transform: rotate(0deg);
        }

        .content-header-select-icon {
            position: absolute;
            width: 20px;
            height: 20px;
            right: 15px;

            svg {
                width: 20px;
                height: 20px;
                color: var(--theme-text-color-lighten);
                position: absolute;

            }
        }

        .content-header-total {
            background-color: aqua;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--theme-text-color-lighten);
            text-align: right;
            margin-left: 20px;
            font-size: 22px;
            line-height: normal;
            -webkit-font-smoothing: antialiased;

            span {
                font-size: 22px;
                color: var(--theme-text-color-darken);
                margin-left: 5px;
            }
        }

        .content-header-total-img-box {
            width: 30px;
            height: 30px;
            display: inline-block;
            margin-left: 10px;

            .rotate {
                animation: spin 1s linear infinite;
            }
        }

        .content-header-total-img {
            height: 30px;
            width: 30px;
        }
    }

    .content-body-rightup{
        position: absolute;
        right: 35px;
        top: 20px;
    }

    .content-body-middle {
        width: 100%;
        height: calc(100vh - 375px);
        padding: 10px 0px;
        //background-color: aquamarine;
    }

    .content-body-bottom {
        width: 100%;
        position: fixed;
        display: flex;
        bottom: 0px;
        height: 95px;
        left: 0px;
        font-size: 22px;
        background-color: var(--theme-sub-bg-color);
      
        border-top:1px solid var(--theme-color-line);
        &-a {
            position: absolute;
            width: 220px;
            padding: 8px 0 0 20px;
        }

        &-b {
            color: white;
            position: absolute;
            padding: 17px 0 0 220px;
        }

        &-c {
            position: absolute;
            width: 200px;
            padding: 8px 20px 0 0px;
            right: 150px;
        }

        &-d {
            color: white;
            position: absolute;
            padding: 17px 70px 0 0px;
            right: 0px;
        }

        &-e {
            position: absolute;
            width: 180px;
            padding: 60px 0 0 20px;
        }

        &-f {
            color: var(--theme-secondary-color-error);
            position: absolute;
            padding: 50px 0 0 220px;
        }
    }
}
</style>

<route lang="yaml">
    meta:
      auth: true
  </route>