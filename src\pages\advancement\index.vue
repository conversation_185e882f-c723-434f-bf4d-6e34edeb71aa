<script setup lang='ts' name='promotion'>
const router = useRouter()
const route = useRoute()
const { isApp } = storeToRefs(useAppStore())
const appStore = useAppStore();
const { secletType } = storeToRefs(appStore);
import Missao from '../promotion-detail/Missao.vue';
import Rebate from '../promotion-detail/Rebate.vue';
import Pendente from '../promotion-detail/Pendente.vue';
import Historico from '../promotion-detail/Historico.vue';
import AppVip from '../../components/AppVip.vue'
import Poupanca from '../promotion-detail/Poupanca.vue';

const currentType = ref('0')
const levelList = [
  { label: 'Tudo', value: '0' }, // 全部
  { label: 'Depósito', value: '1' }, // 存款活动
  { label: 'Baixar APP', value: '2' }, // 未知
  { label: 'Desconto', value: '3' }, // 未知
  { label: 'Classificação', value: '4' }, // 未知
  { label: 'Outros', value: '5' }, // 箱子、签到
]

const SECLET_TYPE = readonly({
    Eventos: '0',
    Missao: '1',
    Vip: '2',
    Rebate: '3',
    Pendente: '4',
    Historico: '5',
    Poupanca: '6',
})

const enum JumpViewType {
  PROMOTION = 0,
  JUROS,
  VIP,
  REBATE,
  PENDENTE,
  HISTORY,
  POUPANCA,
}

// const secletType = computed(() => {
//     if(route.query.key){
//         if(route.query.key == JumpViewType.VIP){
//             // console.log("dd=="+window.location.host)
//             // console.log("ff=="+window.location.pathname)
//             // let info = window.location.host + window.location.pathname
//             // console.log("地址-"+info)
//             // window.location.href = info
//             // console.log("地址111-"+info)
//             return SECLET_TYPE.Vip
//         }
//     }
//     return secletType?secletType:SECLET_TYPE.Eventos
// })

const key = route.query.key;

// const secletType = ref(SECLET_TYPE.Eventos)

// Local state for AppTab v-model (string)
const localSelectedTab = ref<string>(SECLET_TYPE.Eventos);

const tabData = ref([
    {
        label: 'Eventos',
        value: SECLET_TYPE.Eventos // Use string values
    },
    {
        label: 'Missão',
        value: SECLET_TYPE.Missao // Use string values
    },
    {
        label: 'VIP',
        value: SECLET_TYPE.Vip // Use string values
    },
    {
        label: 'Rebate',
        value: SECLET_TYPE.Rebate // Use string values
    },
    {
        label: 'Pendente',
        value: SECLET_TYPE.Pendente // Use string values
    },
    {
        label: 'Poupança',
        value: SECLET_TYPE.Poupanca // Use string values
    },
    {
        label: 'Histórico',
        value: SECLET_TYPE.Historico // Use string values
    },
])


function onTabChange(){
    // This function might not be needed if v-model handles the change
}

// This function is called by AppVip component (presumably with a number index)
function onTabChange2(idx: number){ 
    // Update the store's state directly
    secletType.value = idx;
}

// Watch for changes from the route query
watch(route, () => {
    const key = route.query.key;
    let targetTab = SECLET_TYPE.Eventos; // Default
    if (key) {
        if (key === 'vip') {
            targetTab = SECLET_TYPE.Vip;
        } else if (key === 'rebate') {
            targetTab = SECLET_TYPE.Rebate;
        } else if (key === 'pendente') {
            targetTab = SECLET_TYPE.Pendente;
        } else if (key === 'history') {
            targetTab = SECLET_TYPE.Historico;
        } else if (key === 'juros') {
            targetTab = SECLET_TYPE.Poupanca; // Assuming juros maps to Poupanca
        } else if (key === 'poupanca') {
            targetTab = SECLET_TYPE.Poupanca;
        } else {
             const numericKey = parseInt(key as string, 10);
             if (!isNaN(numericKey)) {
                 // Find the string value corresponding to the numeric key
                 const foundEntry = Object.entries(SECLET_TYPE).find(([name, value]) => parseInt(value, 10) === numericKey);
                 if (foundEntry) {
                     targetTab = foundEntry[1];
                 }
             }
        }
    }
    // Update local state (string) for AppTab
    localSelectedTab.value = targetTab;
    // Update store state (number) if it differs
    const numericTarget = parseInt(targetTab, 10);
    if (!isNaN(numericTarget) && secletType.value !== numericTarget) {
         secletType.value = numericTarget;
    }

}, { immediate: true })

// Watch for changes in the store's secletType (number) and update local state (string)
watch(secletType, (newVal) => {
    const stringVal = String(newVal);
    if (localSelectedTab.value !== stringVal) {
        // Check if the string value exists in SECLET_TYPE before setting
        if (Object.values(SECLET_TYPE).includes(stringVal)) {
            localSelectedTab.value = stringVal;
        }
    }
}, { immediate: true });

// Watch for changes in the localSelectedTab (string) driven by AppTab clicks and update store state (number)
watch(localSelectedTab, (newVal) => {
    const numericVal = parseInt(newVal, 10);
    if (!isNaN(numericVal) && secletType.value !== numericVal) {
        secletType.value = numericVal;
    }
});

onMounted(() => {
    // Initial route processing is handled by the immediate watch(route, ...)
})

</script>
<template>
  <div class="promotion">
    <div class="tab">
        <!-- Use localSelectedTab (string) for v-model -->
        <AppTab :list-data="tabData" v-model="localSelectedTab" @click="onTabChange" :height="88" :class="{border:true}"></AppTab>
    </div>
    <div class = "promotion-content">
        <div class="content">
            <!-- Use localSelectedTab (string) for conditional rendering -->
            <div v-if="localSelectedTab == SECLET_TYPE.Eventos" class="content-eventos">
                <AppAllPromotion/> 
            </div>
            <div v-else-if="localSelectedTab == SECLET_TYPE.Missao" class="content-missao">
                <Missao/>
            </div>
            <div v-else-if="localSelectedTab == SECLET_TYPE.Vip" class="content-vip">
                <AppVip @onclickVip="onTabChange2"/> 
            </div>
            <div v-else-if="localSelectedTab == SECLET_TYPE.Rebate" class="content-rebate">
                <Rebate/>
            </div>
            <div v-else-if="localSelectedTab == SECLET_TYPE.Pendente" class="content-pendente">
                <Pendente/>
            </div>
            <div v-else-if="localSelectedTab == SECLET_TYPE.Historico" class="content-historico">
                <Historico/>
            </div>
            <div v-else-if="localSelectedTab == SECLET_TYPE.Poupanca" class="content-poupanca">
                <Poupanca/>
            </div>
        </div>
    </div>
    
  </div>
</template>

<style lang='scss' scoped>
.promotion{
    .promotion-content{
        position: absolute;
        height: calc(100% - 164px);
        width: 100%;
        overflow: auto;
        .content {
            position: relative;
            width: 100%;
            height: 100%;
            background-color: var(--theme-bg-color);
        }
    }
}

.content-poupanca{
    height: auto;
    background-color: var(--theme-bg-color);
}
</style>

