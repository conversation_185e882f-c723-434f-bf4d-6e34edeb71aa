// 获取Token
export function ApiGetToken() {
  return request<{
    token: string
  }>({
    url: '/token',
    method: 'get',
  })
}

// 用户信息
export function ApiGetUserInfo() {
  return request<{
    age: number
    name: string
    profession: string
  }>({
    url: '/getInfo',
    method: 'get',
  })
}

/**
 * 登录
 * @returns
 */
// 登录
export const ApiLogin = (data: {
  username: string
  password: string
  // device_no?: string
  // vid?: string
  // code?: string
}) => request<string>({ url: '/user/login', method: 'post', data })

/**
 * 用户信息
 */
// 获取会员资料
export const ApiGetMemberInfo = () => request<{
  [text: string]: any
  avatar: number | string // 头像
  created_at: number // 注册时间
  created_ip: string // 注册IP
  deposit_amount: number // 存款总额
  email: string // 邮箱
  grand_id: string // 曾祖父ID
  grand_name: string // 曾祖父名称
  great_grand_id: string // 祖父ID
  great_grand_name: string // 祖父名称
  next_deposit: string // 距离下一级存款金额
  next_level: number // 距离下一级存款金额
  now_deposit: string // 当前存款金额
  now_valid_amount: string; // 当前流水
  parent_id: string // 父ID
  parent_name: string // 父名称
  phone: string // 手机号
  rate: string // 存款比例
  score: number // 积分
  top_id: string // 顶级ID
  top_name: string // 顶级名称
  uid: string // 用户ID
  username: string // 用户名
  vip: number // VIP等级
  invite_num: number // 邀请人数
  last_treasure: number // 上一次申请的宝箱对应的邀请人数
  contate: string // 小飞机频道
  invite_code: string // 邀请码
  pay_password: string // 支付密码
  telegram: string // 小飞机
  phone_verify: string // 手机是否验证 1是0否
  email_verify: string // 邮箱是否验证 1是0否
}>({ url: '/user/userInfo' })

/**
 * 注册
 */
export function ApiRegister(data: {
  phone?: string;
  username?: string;
  password: string;
  real_name?: string;
  currency?: string;

  // reg_url: string;
  link_id?: string;
  business_id?: string;
  channel_id?: string;
}) {
  return request<{
    token: string
  }>({
    url: '/user/reg',
    method: 'post',
    data
  })
}

// 邮箱注册
export const ApiEmailCode = (data: {
  mail: string;
  username?: string; // 忘记密码传
  ty: '1' | '2' // 1 注册 2 忘记密码
}) => request<string>({ url: '/sms/send/mail', method: 'post', data })

// 获取用户余额信息(中心钱包和锁定钱包)
export const ApiGetUserBalance = () => request<{
  uid: string; //用户id
  brl: number; //可下注金额
  brl_amount: number;
  lockAmount: number;
  agencyAmount: number;
  deposit_amount: number;
  deposit_balance: number;
  depositLockAmount: number;
  agency_balance: number;
  agencyLockAmount: number;
  unlock_amount: number;
  agency_lock_amount: number;
}>({ url: '/user/balance' })

// 发送离线验证码
export const ApiSendOfflineSms = (data: {
  tel: string;
  flag: 'text' | 'voice';
  ty: 1 | 2
}) => request<string>({ url: '/sms/send', method: 'post', data })

// 首页最新赢家列表
export const ApiGetMemerLastWin = () => request<{}>({ url: '/user/lastwin', method: 'get' })

// 公告
export const ApiGetMemerNotice = () => request<{
  id: string;
  title: string;
  content: string;
  redirect: string;
  redirect_url: string;
  state: string;
  created_at: number;
  created_uid: string;
  created_name: string;
}[] | null>({ url: '/user/notice', method: 'get' })

// Banner
export const ApiGetMemerBanner = () => request<{
  id: string;
  title: string;
  redirect_url: string;
  images: string;
  seq: number;
  url_type: number; //1内链接，2外链接。字段JumpType
  updated_name: string;
  updated_uid: string;
  updated_at: number;
  state: string;
  created_at: number;
  h5img: string;
}[] | null>({ url: '/user/banners', method: 'get' })



// activepopup
export const ApiGetActivepopup = () => request<{
  id: string;
  images: string;
  length:string;
  redirect_url: string;
  sort:string;
  width:string;
  jump_type:number   //1内链接，2外链接。字段JumpType
}[] | null>({ url: '/configuration/Announcement', method: 'get' })



// vip等级信息
export const ApiGetMemberVip = () => request<{
  vip: number;
  name: string;
  deposit_amount: number;
  flow: number;
  amount: string;
  free_withdraw_num: number;
  withdraw_limit: number;
  rebate_rate: string;
  props: number;
  updated_at: number;
  created_at: number;
}[]>({ url: '/user/vips' })

/* 💬 站内信 💬 */
// 列表
export const ApiGetMessageList = (params: { page: number, page_size: number }) => request<{
  t: number;
  s: number;
  d: {
    id: string;
    ts: string;
    msg_id: string;
    username: string;
    title: string;
    content: string;
    is_top: number;
    is_vip: number;
    ty: number;
    is_read: number;
    send_name: string;
    send_at: number;
  }[];
}>({ url: '/user/message/list', params })
// 读取
export const ApiReadMessage = (data: { id: string }) => request({ url: '/user/message/read', method: 'post', data })
// 删除
export const ApiDeleteMessage = (data: { ids: string }) => request({ url: '/user/message/delete', method: 'post', data: { flag: 1, ...data } })



/* 🎪 活动 🎪 */
// 签到配置
export const ApiGetPromotionSignConfig = () => request<{
  vip: number;
  sign1_amount: string;
  sign2_amount: string;
  sign3_amount: string;
  sign4_amount: string;
  sign5_amount: string;
  sign6_amount: string;
  sign7_amount: string;
}[]>({ url: '/user/sign/config' })
// 签到状态
export const ApiGetPromotionSignRecord = () => request<{
  uid: string;
  username: string;
  vip: number;
  sign1: string;
  sign2: string;
  sign3: string;
  sign4: string;
  sign5: string;
  sign6: string;
  sign7: string;
  last_sign: string;
}>({ url: '/user/sign/record' })
// 进行签到
export const ApiMemberSign = (weekDays: number, monthDays: number) => request({ url: '/user/sign', params: { weekDays, monthDays } })
// 签到记录
export const ApiGetSignRewardRecord = () => request<{
  id: string;
  uid: string;
  username: string;
  vip: number;
  day: number;
  month_day: number;
  amount: string;
  created_at: number;
}[]>({ url: '/user/sign/reward/record' })

// 宝箱配置
export const ApiGetRewardBoxConfig = () => request<{
  id: string;
  invite_num: number;
  amount: string;
  total_amount: string
}[]>({ url: '/user/treasure/config' })
// 宝箱记录
export const ApiGetRewardBoxRecord = () => request<{
  id: string;
  uid: string;
  username: string;
  invite_num: number;
  amount: string;
  created_at: number;
}[]>({ url: '/user/treasure/record' })
// 宝箱申请
export const ApiApplyRewardBox = (invite_num: number) => request({ url: '/user/treasure/apply', params: { invite_num } })

// 周投注活动
export const ApeGetRefundConfig = () => request<{
  bonus_amount: number;
  config_list: {
    id: number;
    flow_amount: number;
    bonus_amount: number;
    updated_at: number;
    updated_name: string;
  }[];
}>({ url: '/user/weekly/config' })

// 获取充值渠道
export const ApiGetDepositChannel = () => request<{
  t: number;
  d: {
    fid: string;
    name: string;
    fmax: string;
    fmin: string;
    amount_list: string;
    show_name: string;
    amount_array: {
      amount: string; //真实金额
      discount: string;
    }[];
    pay_rate: number;
    ty: number;
  }[];
  max: number;
  min: number;
  c: string;
}>({ url: '/payment/channels', method: 'get' })

// 发起充值
export const ApiDoDeposit = (data: {
  fid: string
  amount: string
  flag: string // 1参与优惠2不参与优惠
}) => request<string>({ url: '/payment/deposit', method: 'post', data })

// 提交提现
export const ApiPostWithdraw = (data: {
  amount: string
  pay_password: string
  fid: string,
  bank_id: string,
  flag:string
}) => request<string>({ url: '/payment/withdraw', method: 'post', data })

// 获取提现配置
export const ApiGetWithdrawConf = () => request<any>({ url: '/payment/takeout/config', method: 'get' })


//充值订单记录
export const UserDepositList = (params?: {
  flag: string //0 所有 ，1 昨天， 2 今天，3 7天， 4 15天， 5 30天
}) => request<{
  d: Array<{
    id: string
    created_at: number
    amount: number
    discount: number
    tot_amount: number
    state: number
  }>
}>({ url: '/payment/deposit/list', method: 'get',  params})


//提现订单记录
export const UserWitdrawList = (params?: {
  flag: string //0 所有 ，1 昨天， 2 今天，3 7天， 4 15天， 5 30天
}) => request<{
  d: Array<{
    id: string
    created_at: number
    amount: number
    fee: number
    state: number
    detail: number
  }>
}>({ url: '/payment/takeout/list', method: 'get',  params })



// 订单记录
export const ApiGetMemberRecord = (params: { flag: string, page: number, page_size: number }) => request<{
  t: number
  d: Array<{
    amount: string
    bill_no: string
    created_at: string
    discount: string
    flag: number
    fname: string
    id: string
    state: number
  }>
}>({ url: '/user/record', method: 'get', params })

// 退出登录
export const ApiLogout = () => request<{}>({ url: '/user/logout', method: 'get' })

// 更新头像
export const ApiUpdateAvatar = (params: { avatar: number }) => request<{}>({ url: '/user/update/avatar', method: 'get', params })

// 忘记密码获取手机号
export const checkAccount = (data:{
  username:string
})=>request<string>({ url:'/user/password/checkAccount',method:'post',data })

// 检测用户手机号并发送验证码
export const checkPhoneAndSendSms = (data:{
  username:string,
  phone:string
})=>request<string>({ url:'/user/password/checkPhoneAndSendSms',method:'post',data })

// 忘记密码
export const ApiResetPwd = (data: {
  sid: string
  ts: string
  phone: string
  code: string
  password: string
}) => request<string>({ url: '/user/password/forget', method: 'post', data })

// 存款优惠配置
export const ApiDepositBonusConf = (params: {
  ty?: 1 | 2 // 1 首存 2 次存
}) => request<{
  id: number;
  name: string;
  bonus: number;
  flow: number;
  max_amount: number;
  min_amount: number;
  ty: number;
}[]>({ url: '/user/promote/depositConfig', method: 'get', params })

// 用户账户金额信息
export const ApiBalanceDetailInfo = () => request<{
  agencyAmount: number
  agencyLockAmount: number
  agency_balance: number
  brl: number
  brl_amount: number
  depositLockAmount: number
  deposit_amount: number
  deposit_balance: number
  lockAmount: number
  uid: string
  unlock_amount: number
  agency_lock_amount: number
}>({ url: '/user/balance', method: 'get' })

// 投注记录
export const ApiGetBetRecord = (params: {
  gt: string
  ty: string
  flag?: string
  page: number
  page_size: number
}) => request<{
  t: number;
  d: {
    id: string
    game_name: string
    bet_time: number
    bet_amount: string
    net_amount: string
  }[] | null;
  s: number;
}>({ url: '/user/game/record', params })


////////////////////////////////////////////////////////////////////////
//邀请奖励配置
export interface ProxyInviteBonusItem{
  valid_Counts:number;//数量
  bonus:number; //奖励
  id:number;
}
export const ApiGetProxyInviteBonus = () => request<ProxyInviteBonusItem[]>({ url: '/configuration/proxyInviteBonus', method: 'get' })

//邀请总数据
export interface ProxyAccuInfo {
  TotalCommi: number;//总佣金
  team_num: number; //邀请人数
  DepositNum: number;//充值人数
  ValidNum: number; //有效人数
  Deposit: number;//总充值金额
  Running: number;//总流水
}
export const ApiGetQueryAccuInfo = () => request<ProxyAccuInfo>({ url: '/Proxy/accuInfo', method: 'get' })



export interface SendQueryTeamData {
  beginTime: number; //开始时间
  endTime: number;   //结束时间
  pageIndex: number; //页数  0 开始
  pageCount: number; //页数据量
}

export interface ProxyTeamData {
  uid: number;//玩家ID
  DonateToParent: number;//对父级的贡献
  first_deposit_mount: number;//首冲金额
  running: number;//总流水
  created_at: number;//绑定时间 
  DonateTime: number;//
}

//邀请明细记录
export const ApiGetQueryTeam = (data: SendQueryTeamData) => request<{
  TotalCount: number,
  TAccuInfo: ProxyAccuInfo,
  ListTeamData: ProxyTeamData[] | null
}>({ url: '/Proxy/queryTeam', method: 'post', data })


export interface ProxyCommiReturnItem {
  deposit: number;       //充值金额
  return_rate: number;   //奖励比例 千分位
}

//代理周佣金配置
export const ApiGetWeekProxyCommi = () => request<{ ProxyCommiReturnItem: [] | null }>({ url: '/configuration/weekProxyCommiReturn', method: 'get' })

export interface ProxyWeekDeposit {
  LastWeekDepositReturn: number;      //上周返还佣金
  WeekDeposit: number;                //当前周充值
  LastDeposit: number;                //上周充值
}

//代理周佣金数据
export const ApiGetQueryProxyWeekDeposit = () => request<ProxyWeekDeposit>({ url: '/Proxy/QueryProxyCommi', method: 'get' })

//代理周佣金领取
export const ApiAchieveProxyWeekDeposit = () => request<{}>({ url: '/Proxy/AchieveProxyCommi', method: 'get' })

export interface ProxySubDepositRecordItem {
  uid: number;       //玩家ID
  amount: number;       //充值金额
  created_at: number;   //日期
}

//代理周充值记录  上周
export const ApiGetProxyLastWeekRecord = () => request<ProxySubDepositRecordItem[] | null>({ url: '/Proxy/lastWeekRecord', method: 'get' })
//本周
export const ApiGeProxyThisWeekRecord = () => request<ProxySubDepositRecordItem[] | null>({ url: '/Proxy/thisWeekRecord', method: 'get' })
/***周充值返利 */
export interface DepositReturnItem {
  deposit: number;       //充值额度
  return_rate: number;   //比例
}
//周充值返还配置
export const ApiGetWeekDepositReturn = () => request<DepositReturnItem[] | null>({ url: '/configuration/weekDepositReturn', method: 'get' })

export interface WeekDepositReturn {
  LastWeekDeposit: number;      //上周充值
  ThisWeekDeposit: number;      //本周充值
  WeekDepositReturn: number;     //可领取
}
//周充值返还查询
export const ApiGetQueryWeekDepositReturn = () => request<WeekDepositReturn>({ url: '/user/weekly/depoist/query', method: 'get' })

//周充值返还领取
export const ApiAchieveWeekDepositReturn = () => request<{}>({ url: '/user/weekly/deposit/Achieve', method: 'get' })

export interface SubDepositRecordItem {
  oid: number;       //订单ID
  amount: number;       //充值金额
  created_at: number;   //日期
}

//周充值记录  上周
export const ApiGetLastWeekRecord = () => request<SubDepositRecordItem[] | null>({ url: '/user/weekly/deposit/lastRecord', method: 'get' })
//本周
export const ApiGeThisWeekRecord = () => request<SubDepositRecordItem[] | null>({ url: '/user/weekly/deposit/thisRecord', method: 'get' })

/***周亏损返利 */
export interface LostReturnItem {
  Running: number;       //亏损数量
  return_rate: number;   //比例
}
//周充值返还配置
export const ApiGetWeekLostReturn = () => request<LostReturnItem[] | null>({ url: '/configuration/weekLostReturn', method: 'get' })

export interface WeekLostReturn {
  Running: number;            //本周亏损
  WeekLostReturn: number;      //可领取
}
//周充值返还查询
export const ApiGetQueryWeekLostReturn = () => request<WeekLostReturn>({ url: '/user/weekly/lost/query', method: 'get' })

//周充值返还领取
export const ApiAchieveWeekLostReturn = () => request<{}>({ url: '/user/weekly/lost/Achieve', method: 'get' })

export interface WeekRunningReturnItem {
  running: number;       //亏损额
  return_rate: number;   //比例
}
//周亏损返还配置
export const ApiGetWeekRunningReturn = () => request<WeekRunningReturnItem[] | null>({ url: '/configuration/weekRunningReturn', method: 'get' })

export interface WeekRunningReturn {
  ThisWeekRunning: number;            //本周亏损
  LastWeekRunning: number;      //上周亏损
  WeekRunningReturn: number;      //可领取
}
//周亏损返还查询
export const ApiGetQueryWeekRunningReturn = () => request<WeekRunningReturn>({ url: '/user/weekly/running/query', method: 'get' })

//周亏损返还领取
export const ApiAchieveWeekRunningReturn = () => request<{}>({ url: '/user/weekly/running/Achieve', method: 'get' })

export interface RunningRecordItem {
  slots_running: number;     
  pes_running: number;      
  cas_running: number;   
  bingo_running: number;      
  jogo_running: number;     
  esport_running: number;  
}

//周亏损记录  上周
export const ApiGetLastWeekRunningRecord  = () => request<RunningRecordItem[] | null >({ url: '/user/weekly/running/lastRecord', method: 'get'})
//本周
export const ApiGetThisWeekRunningRecord  = () => request<RunningRecordItem[] | null >({ url: '/user/weekly/running/thisRecord', method: 'get'})

export interface WeekReturn{
  week_deposit_return:number;
  week_bet_return:number;
  week_lost_return:number;
  week_proxy_commi_return:number;
}
export const ApiGetQueryAllReturn  = () => request<RunningRecordItem[] | null >({ url: '/user/weekly/queryAllReturn', method: 'get'})


//日投注返利配置
export interface DailyRunningRecordItem {  
  slots_running: number;     
  pes_running: number;      
  cas_running: number;   
  bingo_running: number;      
  jogo_running: number;     
  esport_running: number;  
}

export interface DailyRunningReturnItem {
  TodayRunning: number;            //今天投注
  YesterDayRunning: number;       //昨天投注
  DailyRunningReturn: number;     //可领取
}

//日投注返利配置
export const ApiGetDayRunningReturn = () => request<WeekRunningReturnItem[] | null>({ url: '/configuration/dailyRunningReturn', method: 'get' })
//日投注返还查询
export const ApiGetQueryDayRunningReturn = () => request<DailyRunningReturnItem>({ url: '/user/Daily/running/query', method: 'get' })
//日投注返还领取
export const ApiAchieveDayRunningReturn = () => request<{}>({ url: '/user/Daily/running/Achieve', method: 'get' })
//日投注记录  昨天
export const ApiGetLastDayRunningRecord  = () => request<DailyRunningRecordItem[] | null >({ url: '/user/Daily/running/lastRecord', method: 'get'})
//日投注记录  今天
export const ApiGetThisDayRunningRecord  = () => request<DailyRunningRecordItem[] | null >({ url: '/user/Daily/running/thisRecord', method: 'get'})
////////////////////////////////////////////////////////////////////////

// 奖励记录
export const ApiGetBonusRecord = (params: {
  ty: string // 6存款优惠 305邀请奖励 307宝箱奖励
  flag: string // 1 今天 7 七天 60 六十天
  page: number
  page_size: number
}) => request<{
  t: number;
  d: {
    id: string;
    created_at: number;
    ty: string;
    amount: string;
    remark: string;
    bet_amount: string;
    net_amount: string;
  }[] | null;
  s: number;
}>({ url: '/user/bonus/record', params })

// 邀请记录
export const ApiGetInviteRecord = (data: any) => request<{
  t: number;
  d: {
    id: string;
    uid: string;
    username: string;
    lvl: number;
    child_uid: string;
    child_username: string;
    first_deposit_at: number;
    deposit_amount: number;
    bonus_amount: number;
    created_at: number;
  }[] | null;
}>({ url: '/user/invite/record', method: 'post', data })

// 邀请明细总计
type LvlData = {
  id: string;
  mem_count: number;
  deposit_mem_count: number;
  first_deposit_bonus: number;
  valid_bet_amount: number;
  rebate_amount: number;
  total_amount: number;
  ty: number;
  report_time: number;
  lvl: string;
  uid: string;
  username: string;
}
export const ApiGetInviteReportTotal = () => request<{
  total_amount: number;
  settled_amount: number;
  pending_amount: number;
  lvl_one: LvlData;
  lvl_two: LvlData;
  lvl_three: LvlData;
}>({ url: '/user/report/detail', method: 'post' })

// 邀请明细
export const ApiGetInviteReport = (data: {
  lvl: string
  start_time: string
  end_time: string
  page: number
  page_size: number
}) => request<{
  t: number,
  d: {
    mem_count: number
    deposit_mem_count: number
    first_deposit_bonus: number
    valid_bet_amount: number
    rebate_amount: number
    total_amount: number
    report_time: string
  }[] | null
}>({ url: '/user/report/list', method: 'post', data })

// 客服链接 {
//     "status": true,
//     "data": ""
// }
export const ApiGetFBTG = () => request<{

}>({ url: '/user/serviceMessages/list', params: { fields: 'telegram,facebook,onlinecs' } })

// 获取app下载地址
export const getAppDownloadUrl = (dv: number) => request<{
  platform: string;
  version: string;
  is_force: number;
  content: string;
  url: string;
}>({ url: '/user/app/upgrade', params: { dv } })

// 在线发送手机验证码
export const ApiOnlinePhoneCode = (data: {
  ty: number // 3=修改密码 4=提现 2=修改邮箱 5=修改手机
  tel?: string
}) => request<string>({ url: '/sms/send/online', method: 'post', data })

// 在线发送邮箱验证码
export const ApiOnlineEmailCode = (data: {
  ty: number // 3=修改密码 4=提现 2=修改邮箱 5=修改手机
  mail?: string
}) => request<string>({ url: '/sms/send/online/mail', method: 'post', data })

// 修改支付密码
export const ApiUpdatePayPwd = (data: {
  update_type:number   // 1 新增密码 2 修改旧密码 3 忘记密码，通过验证码重置
  ty: number           // 1 手机号 2 邮箱的验证码
  sid: string
  ts: string
  code: string
  confirm_password: string
  password: string
  old?: string
  phone?:string
}) => request<string>({ url: '/user/pay/password/modify', method: 'post', data })

// 修改登录密码
export const ApiUpdateLoginPwd = (data: {
  old_password: string
  password: string
  confirm_password: string
}) => request<string>({ url: '/user/password/update', method: 'post', data })

// 绑定手机
export const ApiBindPhone = (data: {
  sid: string
  ts: string
  code: string
  phone: string
}) => request<any>({ url: '/user/bind/phone', method: 'post', data })

// 绑定邮箱
export const ApiBindEmail = (data: {
  sid: string
  ts: string
  code: string
  email: string
}) => request<any>({ url: '/user/bind/email', method: 'post', data })

// 银行卡列表
export const ApiBankList = () => request<{
  //卡信息
  d: Array<{
    created_at: number
    flag: number
    id: string
    pix_account: string
    pix_id: string
    real_name: string
    state: number
    uid: string
    username: string
  }>;
  //列表数量
  t: number
  //绑卡数量限制
  card_limit:number
}>({ url: '/user/bankcardList', method: 'get' })

// 银行卡类型列表
export const ApiBankTypes = () => request<{
  d: Array<{
    bankcode: string
    bankname: string
    id: string | number
    state: string | number
  }>,
  t: number
}>({ url: '/user/banktype/list', method: 'get' })

// 绑定银行卡
export const ApiBindBank = (data: {
  pix_id: string
  pix_account: string

  pay_password?: string
  flag?: string    //1cpf  2phne 3email
  realname?: string
}) => request<any>({ url: '/user/bankcard/insert', method: 'post', data })

// 更新个人信息
export const ApiUpdateUserInfo = (data: {
  phone: string
  email: string
  username: string
  telegram?: string
}) => request<any>({ url: '/user/update/info', method: 'post', data })

// 提现手续费
export const ApiWithdrawFee = (params: {
  amount: number | string
}) => request({ url: '/payment/withdraw/rate', method: 'get', params })

//转盘列表
export const ApiTurnTableList = () => request({ url: '/user/turntable/list', method: 'get' })

// 转盘信息
export const ApiTurnTableInfo = () => request({ url: '/user/turnTable/info', method: 'get' })

// 转盘点击
export const ApiTurnTableClick = (params: {
  uid: number | string
}) => request({ url: '/user/turnTable/click', method: 'get', params })

// 检测转盘开关是否开启
export const ApiTurnTableSwitch = () => request({ url: '/user/turnplate/switch', method: 'get' })

// 奖励金额领取申请
export const ApiTurnTableApply = (data: {
  uid: string
  username: string
  amount: number
}) => request<any>({ url: '/user/turnTable/apply', method: 'post', data })


//充值奖励活动 
export const RechargeActivity = () => request<{
  deposit_amount: number;
  state:number
  item: {
    accu_deposit_amount: number;
    bonus: number;
  }[];
 
}>({ url: '/user/total/deposit', method: 'get' })


//充值奖励活动 
export const getRechargeActivity = (data: {
  state: number
}) => request<{
  money:number
  state:number
}>({ url: '/user/action/accuBonus', method: 'post' ,data})



export interface PromoInspection {
  ID: string,
  ChangeMoney: number,// 金额变化
  NeedWageRequire: number, //需要的打码量
  CurWageRequire: number, //当前打码量
  Status: number  // 状态 0=未完成 1=已完成
}
//提现界面的打码列表
export const ApiGetFlowRecordList = (data: {
  QueryType: number // 1全部 2 没有开始 3 进行中 4 已完成
}) => request<PromoInspection[] | null>({ url: '/user/flow/record/list', method: 'post' ,data})

export interface PlatformLinkData {
  telegram: string,
  facebook: string,
  twitter: string, 
  instagram: string,
  AppDownloadBonus:string,
  ValidInviteWagedReq:number,
  ValidInviteMinDeposit:number,
  valid_invite_condition:number,
  CfgWithdrawPixType:number,
  cfg_deposit_cpf_switch:number,
}
//频道配置
export const ApiGetPlatformLinkData = () => request<PlatformLinkData | null>({ url: '/configuration/PlatformLinks', method: 'get' })

//充值奖励活动 
export const AppDownloadBonus = () => request<{
  data:number
 }>({ url: '/user/action/appDownloadBonus', method: 'get' })
 
//走马灯
export interface PlatformLinkData {
  status:number,
  data: [{id:number,type:number,content:string,confirm_at:string}],
}
//频道配置
export const ApiGetBroadCast = () => request<PlatformLinkData | null>({ url: '/configuration/broadcast', method: 'get' })


export interface MailListItem{
    MailId:number,
    title:string,
    content:string,
    time:string,
    state:string,
  }
  
  //邮箱列表请求
  export const ApiGetMailListData = () => request<MailListItem[]>({ url: '/user/mail/list', method: 'get' })

//vip配置
export interface CfgVipItem{
    level:number,
    needRunning:number,
    upBonus:number,
    weekBonus:number,
    monthBonus:number,
}  
export const ApiGetVipListData = () => request<CfgVipItem[] | null>({ url: '/configuration/vipCfg', method: 'get' })


//一键领取VIP所有奖励
export const ApiGetVipMemberGroupData = () => request({ url: '/user/vipBonus', method: 'get' })

export interface VipInfoItem{
    level:number,
    canBonus:number,
    validWaged:number,
}

export const ApiGetVipMemberInfoData = () => request({ url: '/user/vipInfo', method: 'get' })


export interface ValidInviteBonusInfo {
  ValidInviteCount:number,
  BonusStatus:number,
}
//邀请宝箱奖励信息
export const ApiGetValidInviteBonusInfo = () => request<ValidInviteBonusInfo | null>({ url: '/Proxy/ValidInviteBonusInfo', method: 'get' })

export const ApiInviteBonus = ( BonusStage:number) => request<any>({ url: '/Proxy/AchieveValidInviteBonus', method: 'post', data: {BonusStage} })


//----------------模盘代理---------------------
export interface RunningReturn {
  ChildCount:number,    //直属下级人数
  ChildRunningReturn:number, //流水返利
  VipLevel:number  //vip
}

//查询下级流水返利 link_de_convite 界面
export const ApiQueryChildRunningReturnData = () => request<RunningReturn>({ url: '/Proxy/QueryChildRunningReturn', method: 'get' })
//领取下级流水返利
export const ApiGetChildRunningReturnData = () => request({ url: '/Proxy/AchieveChildRunningReturn', method: 'get' })


export interface DirectChildInfo {
  TeamCounts:number,    //直属下级人数
  FirstDepositCounts:number, //首充人数
  DepositCounts:number,   //总充值人数
  DepositNums:number,  //直属充值金额
  Waged:number,    //直属下级流水
  Commi:number,   //佣金
  first_deposit_to_parent:number, //首充梯度奖励佣金
}
//查询直属信息 mesu_dados界面
export const ApiQueryProxyDirectChildInfo = (data: {
  QueryType: number
}) => request<DirectChildInfo>({ url: '/Proxy/QueryProxyDirectChildInfo', method: 'post' ,data})


export interface ProxyChildInfoItem {
  ChildCounts:number,   //直属下级人数
  OtherCounts:number,   //二三级人数

  ChildWaged:number,   //直属流水
  OtherdWaged:number,  //二三级流水

  ChildWageReturn:number,    //直属流水返利
  OtherWageReturn:number,   //二三级佣金（下级流水返利）

  ChildCommi:number,    //直属佣金
  OtherCommi:number,   //二三级佣金

  enable_first_deposit_to_parent:number, //首充梯度奖励佣金
}
//查询直属信息 mesu_dados界面
export const ApiQueryProxyChildInfo = () => request<ProxyChildInfoItem>({ url: '/Proxy/QueryProxyChildInfo', method: 'get'})


//按时间查询信息 todos os dados
export interface SendQueryTeamDataTodos {
  beginTime: string; //开始时间
  endTime: string;   //结束时间
  pageIndex: number; //页数  0 开始
  pageCount: number; //页数据量
  uid:number ;    //id
}

export interface ListTeamData {
  uid: number; //
  created_at: number;   //绑定时间
  running: number; //流水
  deposit: number; //充值
  VipLevel: number;  //vip
}

export interface ProxyChildItem {
  ChildCounts: number; //直属下级人数
  OtherCounts: number;   //二三级人数
  ChildWaged: number; //直属流水
  OtherdWaged: number; //二三级流水
}

//邀请明细记录
export const ApiGetQueryTeamTodos  = (data: SendQueryTeamDataTodos) => request<{
  TAccuInfo: ProxyChildItem,
  TotalCount: number,
  ListTeamData: ListTeamData[] | null
}>({ url: '/Proxy/queryTeam', method: 'post', data })

export interface ProxyChildDetailInfo {
  username: string; //昵称
  createdAt: number;   //绑定时间
  totalWaged: number; //总流水
  FirstDepositAmount: number;  //首冲金额
}

export interface ProxyChildDetailInfoParam {
  pageIndex: number; //页数
  pageCount: number;   //总页数
  Username: number; //用户名
  QueryType: number;  // 0 全部 1 有效 2 无效
}
//有效邀请明细记录
export const ApiGetProxyChildDetailInfoItem  = (data: ProxyChildDetailInfoParam) => request<{
  ValidInviteWagedReq: number,
  ValidInviteMinDeposit: number,
  TotalCount: number,
  ListData: ProxyChildDetailInfo[] | null
}>({ url: '/Proxy/QueryProxyChildDetailInfo', method: 'post', data })


export interface verificationPasswordParam{
    PayPassword:string; //支付密码
}

export const ApiGetVerificationPasswordParam  = (data: verificationPasswordParam) => request<{
    pay_password:string
  }>({ url: 'user/pay/password/verification', method: 'post', data })



 export interface insertBankParam{
    PixId:string;
    Realname:string;
    Flag:string;
    Account:string;
 } 


 export const ApiGetInsertBankParam  = (data: insertBankParam) => request<{
    pix_id:string,
    realname:string,
    flag:string,
    pix_account:string,
  }>({ url: 'user/bankcard/insert', method: 'post', data })

export interface updateBankParam {
  pix_id:string,
  state:number,
}
//默认账号
export const ApiUpdateBankState = (data: updateBankParam) => request({ url: 'user/bankcard/update/state', method: 'post', data })

export interface CfgSuspensionImagesParam {
  id:number,
  images:string,            //图片链接
  jump_type:number,         //1内链接，2外链接
  sort:number,              //排序
  status:number,            //状态 0 关闭  1开启
  display_method:number,    //显示方式 1=独立 2=合并
  url:string,               //跳转链接
  pc_images:string,         //pc端图片
  length:string,
  width:string,
}

export interface ActiveSwitchCfg {
  active_type:number,
  active_name:string,
  status:number,
  popup_switch:number
}
export const ApiActiveSwitchList = () => request<any>({ url: 'configuration/activitySwitch/list', method: 'get'})


export interface UserDepositDiscount {
  id:string,
  discount:number,
}
export const ApiUserDepositDiscount = () => request<any>({ url: '/payment/deposit/discount/list', method: 'get'})

export const ApiGetUserDepositDiscount = (data: {id: string, discount: number}) => request<{
  id:string,
  discount:number,
}>({ url: '/payment/deposit/discount/apply', method: 'post', data })

/** 分成比例配置 */
export interface RechargeShareCfg {
  level: number;       //亏损数量
  rate: number;   //比例
}
//下级充值分成配置
export const ApiRechargeShareList = () => request<{
  cfgList: RechargeShareCfg[] | null,
  validWaged: number,     //下级打码量要求
  Lv1FlowMul: number,
  Lv2FlowMul: number,
  Lv3FlowMul: number,
}>({ url: '/configuration/rechargeShareCfg', method: 'get' })

/*
充值分成的信息
*/
export interface RechargeShareInfo {
  EnableRechargeShare: number;    //可领取金额
  HasRechargeShare:number;        //已领取金额
}
export const ApiRechargeShareInfo = () => request({ url: '/user/rechargeShareInfo', method: 'get' })

/*
领取充值分成
*/
export const ApiRechargeShare = () => request<any>({ url: '/user/rechargeShare', method: 'get'})

/*
查询充值分成记录
*/
export interface ShareRecordtemParam {
  beginTime: number; //开始时间
  endTime: number;   //结束时间
  level: number;     //一级、二级、三级
  pageIndex: number; //页数  0 开始
  pageCount: number; //页数据量
}

export interface ShareRecordTotal {
  amount: string;
  recharge_share: number;
}

export interface ShareRecordItem {
  uid: number;
  nickname: string;
  parent_id: number;
  amount: number;
  level: number;
  recharge_rate: number;
  recharge_share: number;
  create_at: number;
}

export interface ShareRecord {
  Total: ShareRecordTotal | null;
  Items: ShareRecordItem[] | null;
}

//查询充值分成记录
export const ApiGetRechargeShareRecord = (data: ShareRecordtemParam) => request<{
  recordCount: number,
  Total: ShareRecordTotal[] | null,
  Items: ShareRecordItem[] | null
}>({ url: '/user/rechargeShareRecord', method: 'post', data })

//---------------------------首充梯度奖励start--------------------------------

export interface ChargeGradientRewardItem {
  deposit: number,    //充值金额
  bonus: number       //奖励
}

//首充梯度奖励配置信息
export const ApiGetChargeGradientRewardCfg = () => request<{
  has_deposit_bonus: number,    //已领取金额
  invite_bonus: ChargeGradientRewardItem[] | null, //首充梯度奖励明细
  need_waged: number            //有效打码配置
}>({ url: '/user/first/deposit/bonus/info', method: 'get'})


export interface ChargeGradientRewardParam {
  beginTime: number; //开始时间
  endTime: number;   //结束时间
  pageIndex: number; //页数  0 开始
  pageCount: number; //页数据量
}

export interface DepositBonusTotal {
  total_deposit: number,
  total_bonus: number,
}

export interface DepositBonusItem {
  uid: number;
  nickname: string;
  parent_id: number;
  amount: number;
  level: number;
  rate: number;
  bonus: number;
  create_at: number;
}

//查询梯度奖励记录
export const ApiGetChargeGradientRewardRecord = (data: ChargeGradientRewardParam) => request<{
  record_count: number,
  deposit_bonus_total: DepositBonusTotal | null,
  Items: DepositBonusItem[] | null
}>({ url: '/user/first/deposit/bonus/record', method: 'post', data })

//---------------------------首充梯度奖励end--------------------------------

export interface MemberVerify {
  real_name: string,// 用户名
  cpf: string, // 密码
}
//
export const ApiGetMemberVerify = () => request< MemberVerify | null>({ url: 'user/verify/info', method: 'get' })

export interface VerifyInfo {
  real_name: string,// 用户名
  // cpf: string, // 密码
}

//
export const ApiUpdateVerifyInfo = (data: VerifyInfo) => request<{
  data: string | null
}>({ url: 'user/update/verify/info', method: 'post', data })

//------------------------储蓄罐begin-------------------------------------
export interface PiggyBankInfo {
  Return: string;                   //倍率
  PiggyAmount: string;              //储蓄罐金额
  Interest: string;                 //利息
  TotalInterest: string;            //已赎回利息
  CanAchieveInterest: number;       //领取状态
  InterestCalctime: number;         //倒计时
}

/*
查询玩家储蓄罐信息
*/
export const ApiGetPiggyBankInfo = () => request<PiggyBankInfo | null>({ url: '/PiggyBank/QueryInterest', method: 'get'})
export interface RecPiggyBankItem {
  uid: string;                   //ID
  amount: string;              //金额
  type: number;                 //类型
  created_at: string;            //时间
}

export interface PiggyBankChangeInfoParam{
  flag: number;//查询时间 0 所有 ,1 昨天, 2 今天,3 7天, 4 15天,5 30天
  type: number;//查询类型 0:储蓄罐转到钱包 1(钱包转到储蓄罐) 2 利息  否则所有
}

//查询储蓄罐 变化记录
export const ApiGetPiggyBankChangeInfo = (params: PiggyBankChangeInfoParam) => request<RecPiggyBankItem[]>({ url: '/PiggyBank/PBChangeRec', method: 'get', params })

//领取当前利息
export const ApiAchieveInterest = () => request<any>({ url: '/PiggyBank/AchieveInterest', method: 'get'})



export const ApiGetDepositParam = (params?: {
  amount:number,
}) => request<{}>({ url: '/PiggyBank/deposit', method: 'get',  params })

export const ApiGetWithdrawParam = (params?: {
  amount:number,
  payp:string,
}) => request<{}>({ url: '/PiggyBank/withdraw', method: 'get',  params })


//------------------------储蓄罐end-------------------------------------
