<script setup lang="ts" name="app-index-game-container">
import Id from "~/pages/index/game-list/[id].vue";
import { GameItem, GameNavEnum } from "~/types/common";
const appStore = useAppStore();
const { isSmallGameIcon, gameItemPageNum, isShowPGSlots, PGSlotsImgType } =
  storeToRefs(appStore);
interface Props {
  id: GameNavEnum;
  filterType?: string;
  favType?: string;
  platform_id?: string;
  showTitle?: boolean;
}

// 分页的列表数据
const gameList = ref<any[]>([]);

const props = withDefaults(defineProps<Props>(), {
  showTitle: true
});
const router = useRouter();
const isShowVertical = ref(false); //切换横竖版
const isShowThreeRow = ref(false); //是否显示三行
const finished = ref(false);
const isLoading = computed(() => {
  return listLoading.value;
});

//重连数据
const isGetDataFinish = ref(false);
const reconnectionTims = ref(0);
const reconnectionMax = ref(3);
let pageItemNum = gameItemPageNum;

let curPage = ref(1);
const total = ref(0);
function clearReconnectionData() {
  isGetDataFinish.value = false;
  reconnectionTims.value = 0;
}

// 首页 获取推荐的 热门 游戏 100
// 首页 获取推荐的 收藏 游戏 101
const {
  run: runGetSpecialRec,
  data: specialRecGames,
  loading: listLoading,
} = useRequest(
  () =>
    +props.id === GameNavEnum.Quente
      ? ApiGameHotList({
          ty: 0,
          platform_id: props.platform_id ? props.platform_id : "0",
          page_size: pageItemNum.value,
          page: curPage.value,
          is_fav: props.filterType === "love" ? 1 : undefined,
        })
      : ApiRecFavGames({
          ty: "0",
          platform_id: "0",
          page_size: pageItemNum,
          page: curPage,
          is_fav: props.filterType === "love" ? 1 : undefined,
        }),
  {
    manual: true,
    onSuccess: (data) => {
      isGetDataFinish.value = true;
      onSuccessPage(data);
    },
  }
);

const { run: runGetNormalRec, data: recGames } = useRequest(
  () =>
    ApiGameRecList({
      ty: +props.id,
      platform_id: props.platform_id ? props.platform_id : "0",
      page_size: pageItemNum.value,
      page: curPage.value,
      is_fav: props.filterType === "love" ? 1 : undefined,
    }),
  {
    manual: true,
    onSuccess: (data) => {
      isGetDataFinish.value = true;
      onSuccessPage(data);
    },
    onAfter: () => {
      if (
        isGetDataFinish.value == false &&
        reconnectionTims.value < reconnectionMax.value
      ) {
        reconnectionTims.value += 1;
        setTimeout(function () {
          console.log(
            "获取数据ApiGameRecList-最后执行=" + reconnectionTims.value
          );
          runGetNormalRec();
        }, 500);
      }
    },
  }
);

// 收藏
const { run: runGetLovedGames, data: favedGames } = useRequest(
  () =>
    ApiLoveGameList({
      ty: _ty.value + "",
      platform_id: props.platform_id ? props.platform_id : "0",
      hot: 0,
      game_type:
        props.id === GameNavEnum.Quente ||
        props.id === GameNavEnum.Dentro_De_Casa
          ? 0
          : props.id,
    }),
  {
    manual: true,
  }
);

let surplusGameData: GameItem | null = null;
const onSuccessPage = (data: any) => {
  if (data) {
    if (curPage.value === 1) {
      total.value = data.t;
      gameList.value = [];
    }
    if (isShowPGSlots.value && props.id === GameNavEnum.Quente) {
      if (surplusGameData) {
        gameList.value = gameList.value.concat(surplusGameData);
        surplusGameData = null;
      }
      if (data.d && data.d.length) {
        // 如果数据长度等于每页数量，则将最后一项数据赋值给 surplusGameData
        if (data.d.length == pageItemNum.value) {
          surplusGameData = data.d.pop();
        }
        gameList.value = gameList.value.concat(data.d);
      }
      if (
        (curPage.value === 1 && gameList.value.length < pageItemNum.value) ||
        (gameList.value.length &&
          total.value > 0 &&
          gameList.value.length >= total.value)
      ) {
        if (!surplusGameData) {
          finished.value = true;
        } else {
          finished.value = false;
        }
      } else {
        finished.value = false;
      }
    } else {
      if (data.d && data.d.length) {
        gameList.value = gameList.value.concat(data.d);
      }
      if (
        (curPage.value === 1 && gameList.value.length < pageItemNum.value) ||
        (gameList.value.length &&
          total.value > 0 &&
          gameList.value.length >= total.value)
      ) {
        finished.value = true;
      } else {
        finished.value = false;
      }
    }
  }
};

const _ty = computed(() => {
  console.log(111);
  if (props.id) {
    if (props.id === GameNavEnum.Dentro_De_Casa) {
      return "fav";
    } else if (props.id === GameNavEnum.Quente) {
      return "hot";
    } else if (props.favType === "rec") {
      return "rec";
    } else {
      return "";
    }
  }
  return "";
});

if (
  +props.id !== GameNavEnum.Quente &&
  +props.id !== GameNavEnum.Dentro_De_Casa
) {
  clearReconnectionData();
  runGetNormalRec();
} else {
  runGetSpecialRec();
}

// const renderList = computed(() => {

//   if (props.filterType === 'love') {
//     return favedGames.value || []
//   }
//   if (+props.id !== GameNavEnum.Quente && +props.id !== GameNavEnum.Dentro_De_Casa) {
//     return recGames.value?.d || []
//   } else {
//     return specialRecGames.value?.d || []
//   }

// })

const goPage = () => {
  // if (props.id === GameNavEnum.Quente) {
  //   return
  // }
  // if (props.id === GameNavEnum.Dentro_De_Casa) {
  //   router.push('/rec-fav-game')
  //   return
  // }
  // router.push('/game-list/' + props.id)

  if (finished.value) return;
  // removeFunc()
  curPage.value = curPage.value + 1;

  if (
    +props.id !== GameNavEnum.Quente &&
    +props.id !== GameNavEnum.Dentro_De_Casa
  ) {
    runGetNormalRec();
  } else {
    runGetSpecialRec();
  }
};

watch(
  () => props.filterType,
  (val, old) => {
    // total.value = 0
    // gameList.value = []
    if (val === "love") {
      runGetLovedGames();
      return;
    }
    if (
      +props.id !== GameNavEnum.Quente &&
      +props.id !== GameNavEnum.Dentro_De_Casa
    ) {
      clearReconnectionData();
      runGetNormalRec();
    } else {
      runGetSpecialRec();
    }
    // if (val === 'hot') {
    //   return
    // }
    // if (val === 'love') {
    //   return
    // }
    // getGameList()
  }
);

//不是在主界面不显示标题
const isShowTitle = ref(true);
// const goldCollect = router.currentRoute.value.fullPath
// let enterGame = GetQueryString("PC");
// if( goldCollect !='/'){
//   isShowTitle.value=false
// }
// //PC端
// if(enterGame){
//   isShowTitle.value=true
// }
// 显示竖版图标
// if(props.id == GameNavEnum.Quente){
isShowVertical.value = true;
// }

//显示三行图标
// if(props.id == GameNavEnum.Slot){
//   isShowThreeRow.value  =true
// }

//点击反向按钮
const scrollView = ref();
function gotoLeft() {
  scrollView.value?.scrollTo(
    scrollView.value.scrollLeft - scrollView.value.clientWidth,
    100
  );
}

function gotoRight() {
  scrollView.value?.scrollTo(
    scrollView.value.scrollLeft + scrollView.value.clientWidth,
    100
  );
}

function appGamePlatformby() {
  console.log("id====" + props.id);
  if (props.id == GameNavEnum.Quente) {
    router.push("/popular");
  } else {
    router.push("/subgame");
  }
}

const gameItemInnerStyle: any = {
  margin: isSmallGameIcon.value
    ? `var(--app-npx-22) var(--app-npx-20)`
    : `var(--app-npx-15) 0`,
};
</script>

<template>
  <div id="app-index-game-container-popular" class="app-index-game-container">
    <AppIndexTitle
      v-if="
        router.currentRoute.value.name !== 'index' &&
        router.currentRoute.value.name !== 'slots-id' &&
        router.currentRoute.value.name !== 'popular-id'
      "
      :id="id"
      :platform_id="platform_id"
      :isCallback="true"
    />
    <div class="app-maps game-container">
      <div class="popular-title" v-if="showTitle && id == GameNavEnum.Quente">
        <div class="popular-fire"></div>
        <div class="title-text">Popular</div>
      </div>
      <div
        ref="scrollView"
        class="content"
        :class="{
          contentHorizontal: !isShowVertical,
          versionContent: isShowVertical,
          contentHorizontalThree: isShowThreeRow,
          'is-empty-loading': !gameList?.length,
        }"
        :style="gameItemInnerStyle"
      >
        <template v-if="gameList && gameList.length > 0">
          <AppGameItem
            v-if="isShowPGSlots && isSmallGameIcon && id == GameNavEnum.Quente"
            class="game-item"
            :data="{ isPGSlots: true, br_alias: 'PG Slots' }"
          />
          <AppGameItemBig
            v-if="isShowPGSlots && !isSmallGameIcon && id == GameNavEnum.Quente"
            class="game-item"
            :data="{ isPGSlots: true, br_alias: 'PG Slots' }"
          />
          <AppGameItem
            v-if="isSmallGameIcon"
            class="game-item"
            v-for="(item, idx) in gameList"
            :key="item.id + idx"
            :data="{
              ...item,
              favType: favType ? favType : id,
              isHot: id == GameNavEnum.Quente,
            }"
          />
          <AppGameItemBig
            v-else
            class="game-item"
            v-for="(item, idx) in gameList"
            :key="item.id + idx"
            :data="{
              ...item,
              favType: favType ? favType : id,
              isHot: id == GameNavEnum.Quente,
            }"
          />
          <!-- <div v-if="isLoading" class="last-loading loading-center">
            <AppSpinner :size="100" :stroke-width="10" color="#1373EF" />
          </div> -->
        </template>
      <!--  <template v-else>
           <div class="loading-container">
            <div
              v-if="isLoading"
              class="last-loading init-loading loading-center"
            >
              <AppSpinner :size="100" :stroke-width="10" color="#1373EF" />
            </div>
          </div> 
        </template>-->
     
      </div>
      <div class="loading-container" v-if="isLoading">
            <div
             
              class="last-loading init-loading loading-center"
            >
              <AppSpinner :size="100" :stroke-width="10" color="#1373EF" />
            </div>
          </div>
      <!-- 获取下一页 -->
      <div class="more" v-if="!finished">
        <label class="more-text"
          >A exibir {{ gameList.length }} jogos entre {{ total }}
          {{ id == GameNavEnum.Slots ? "Slots" : "" }} jogos</label
        >
        <div class="more-text-carregar" @click="goPage">
          Carregar mais
          <AppImage
            class="more-text-img"
            src="/icons/downcarregar.webp"
            alt=""
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
#app-index-game-container-popular {
  scroll-margin-top: 27.333333vw; /* 设置向上偏移的距离 */
  scroll-snap-align:start;
}
.app-index-game-container {
  position: relative;
  color: var(--app-title-color);
  font-size: var(--app-gameTitle-fontSize);
  padding: 15px 0;
  line-height: 40px;
}

.popular-title {
  display: flex;
  align-items: center;
  margin-bottom: 10px;

  .popular-fire {
    min-width: 44px;
    height: 40px;
    background: url("/icons/nav_100.png.webp") no-repeat;
    background-size: contain;
    // margin-right: 8px;
  }

  .title-text {
    font-size: 28px;
    // font-weight: bold;
    color: var(--app-title-color);
  }
}

.app-maps.game-container {
  margin-left: -8px;
  margin-right: -8px;
  border-radius: 0;
  padding-left: 8px;
  padding-right: 8px;
}

.app-maps {
  .content {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1px;
    height: 100%;
    min-height: 200px;

    @media (min-width: 480px) {
      grid-template-columns: repeat(3, 1fr);
    }

    &::-webkit-scrollbar {
      display: none;
    }

    &.is-empty-loading {
      align-items: center;
      justify-items: center;
      justify-content: center;
    }

    .game-item {
      width: 100%;
      position: relative;
    }

    .last-loading {
      grid-column: 1 / -1;
      height: 100px;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
    }
 
  }
  .loading-container {
      width:100%;

      // width: 650px;
      text-align: center;
      margin: 0 auto;
    }
    .loading-center {
      width:100%;
      text-align: center;
      justify-content: center;
      align-items: center;
      img {
        margin: 0 auto;
      }
    }
  .more {
    text-align: center;
    padding-top: 25px;
    border-radius: 10px;

    .more-text {
      display: block;
      color: var(--theme-text-color-lighten);
      font-size: 18px;
    }

    .more-text-carregar {
      display: inline-block;
      color: var(--theme-text-color);
      font-size: 18px;
      margin-top: 5px;
    }

    .more-text-img {
      width: 14px;
      vertical-align: middle;
    }
  }
}
</style>
