<script setup lang="ts">
import { GameNavEnum, CycleModeEnum, GameHallTopEnum } from "~/types/common";

const appStore = useAppStore();
const { isShowAppBaixar } = storeToRefs(appStore);
const gameStore = useGameStore();
const router = useRouter();
const route = useRoute();
const { id } = route.params;
const clickId = ref(+id);

const {
  showLeftMenu,
  curCycleMode,
  isPlayingMusic,
  iosInfoShow,
  secletType,
  showMusicPlayer,
} = storeToRefs(appStore);

const menus = [
  { path: "/", id: GameHallTopEnum.Popular, cateName: "Popular" },
  { path: "/subgame", id: GameHallTopEnum.Solts, cateName: "Slots" },
  {
    path: "/subgame/recente",
    id: GameHallTopEnum.Recente,
    cateName: "Recente",
  },
  {
    path: "/subgame/favoritos",
    id: GameHallTopEnum.Favoritos,
    cateName: "Favoritos",
  },
];

const liClick = (item: any) => {
  console.log(JSON.stringify(item));
  clickId.value = item.id;
  showLeftMenu.value = false;
  if (item.path) {
    router.push(item.path);
    return;
  }
  router.push(`/game-list/${item.id}`);
};

watch(
  route,
  (val, old) => {
    if (val.path === "/") {
      clickId.value = GameHallTopEnum.Popular;
      return;
    }
    if (val.path === "/subgame/recente") {
      clickId.value = GameHallTopEnum.Recente;
      return;
    }
    if (val.path === "/subgame/favoritos") {
      clickId.value = GameHallTopEnum.Favoritos;
      return;
    }

    // if (val.path === '/Slots'){
    //   clickId.value = GameNavEnum.Slots
    //   return
    // }
    // if (val.path.indexOf('/game-list/') === -1) {
    //   clickId.value = -1
    // } else {
    //   clickId.value = +route.params.id
    // }
  },
  { immediate: true }
);

// const showMusicPlayer = ref(false);
const audioPlayer = ref(null);
const progress = ref(0);
const duration = ref(0);

const currentTime = ref(0);
const durationTime = ref("00:00");
const audioSrc = ref("");
const curMusicIndex = ref(0);
const curMusicId = ref(1);
const curMyMusicIndex = ref(0);
let myMusicArray = [1];
const IsOnlyOne = ref(true);

function setMusicData() {
  // localStorage.removeItem('myMusicArray');
  const storedArrayString = localStorage.getItem("DownloadMusicArray");
  if (storedArrayString) {
    const storedArray = JSON.parse(storedArrayString);
    musicNum.value = storedArray.length;
    myMusicArray.splice(0);
    musicData1.value.splice(0);
    for (let i = 0; i < storedArray.length; i++) {
      let item = musicData.value.find(
        (item) => item.id == Number(storedArray[i])
      );
      item.state = MUSIC_STATE.Downloaded;
      myMusicArray.push(Number(storedArray[i]));
      musicData1.value.push(item);
    }
  } else {
    musicData.value[0].state = MUSIC_STATE.Downloaded;
    musicData1.value.push(musicData.value[0]);
  }
  audioSrc.value =
    brazilImg +
    "/music/" +
    musicData1.value[0].name +
    musicData1.value[0].format;
  curMusicId.value = musicData1.value[0].id;
  curMusicIndex.value = musicData1.value[0].id - 1;

  IsOnlyOne.value = false;
  if (myMusicArray.length == 1) {
    IsOnlyOne.value = true;
  }
  console.log("setMusicData");
}

function togglePlay() {
  isPlayingMusic.value = !isPlayingMusic.value; // 切换播放状态
  // 这里可以添加实际控制音乐播放的逻辑
  if (isPlayingMusic.value == true) {
    if (audioPlayer.value) {
      audioPlayer.value.play();
    }
  } else {
    if (audioPlayer.value) {
      audioPlayer.value.pause();
    }
  }
}

const onChange = (e) => {
  audioPlayer.value.currentTime =
    (audioPlayer.value.duration * progress.value) / 100;
};

const updateProgress = () => {
  if (audioPlayer.value.duration) {
    const newTime = audioPlayer.value.currentTime;
    currentTime.value = newTime.toFixed(2);
    progress.value = (newTime / audioPlayer.value.duration) * 100;
  }
};

const updateTime = () => {
  if (audioPlayer.value) {
    duration.value = audioPlayer.value.duration;
    durationTime.value = formatTime(duration.value);
    if (isPlayingMusic.value == true) {
      audioPlayer.value.play();
    }
  }
};

onMounted(() => {
  setMusicData();
  setCurSongName();
  audioPlayer.value = new Audio(audioSrc.value);
  audioPlayer.value.load();
  audioPlayer.value.pause();
  audioPlayer.value.addEventListener("timeupdate", updateProgress);
  audioPlayer.value.addEventListener("loadedmetadata", updateTime);
});

// 格式化时间函数
const formatTime = (time) => {
  const minutes = Math.floor(time / 60);
  const seconds = Math.floor(time % 60);
  return `${minutes.toString().padStart(2, "0")}:${seconds
    .toString()
    .padStart(2, "0")}`;
};

function previousSong() {
  // 添加逻辑以播放前一首歌曲
  if (curCycleMode.value == 1 || curCycleMode.value == 3) {
    curMyMusicIndex.value =
      (curMyMusicIndex.value - 1 + musicData1.value.length) %
      musicData1.value.length;
  } else if (curCycleMode.value == 2) {
    getRandomMusicIndex();
  }
  curMusicId.value = musicData1.value[curMyMusicIndex.value].id;
  curMusicIndex.value = musicData1.value[curMyMusicIndex.value].id - 1;
  loadSong(musicData1.value[curMyMusicIndex.value]);
  setCurSongName();
}

function nextSong() {
  // 添加逻辑以播放下一首歌曲
  if (curCycleMode.value == 1 || curCycleMode.value == 3) {
    curMyMusicIndex.value =
      (curMyMusicIndex.value + 1) % musicData1.value.length;
  } else if (curCycleMode.value == 2) {
    getRandomMusicIndex();
  }
  curMusicId.value = musicData1.value[curMyMusicIndex.value].id;
  curMusicIndex.value = musicData1.value[curMyMusicIndex.value].id - 1;
  loadSong(musicData1.value[curMyMusicIndex.value]);
  setCurSongName();
}

const playNextSong = () => {
  if (curCycleMode.value == 1) {
    curMyMusicIndex.value =
      (curMyMusicIndex.value + 1) % musicData1.value.length;
  } else if (curCycleMode.value == 2) {
    getRandomMusicIndex();
  }
  curMusicId.value = musicData1.value[curMyMusicIndex.value].id;
  curMusicIndex.value = musicData1.value[curMyMusicIndex.value].id - 1;
  loadSong(musicData1.value[curMyMusicIndex.value]);
  setCurSongName();
};

function getRandomMusicIndex() {
  if (musicData1.value.length == 1) return;
  let randomIndex;
  do {
    randomIndex = Math.floor(Math.random() * musicData1.value.length);
  } while (randomIndex === curMyMusicIndex.value);
  curMyMusicIndex.value = randomIndex;
}

function musicItemClick(index, state) {
  if (state == MUSIC_STATE.Downloaded) {
    isPlayingMusic.value = true;

    if (index != curMusicIndex.value) {
      curMusicIndex.value = index;
      curMusicId.value = musicData.value[curMusicIndex.value].id;
      curMyMusicIndex.value = musicData1.value.findIndex(
        (item) => item.id == curMusicId.value
      );
      loadSong(musicData.value[curMusicIndex.value]);
      setCurSongName();
    } else {
      if (isPlayingMusic.value == true) {
        if (audioPlayer.value) {
          audioPlayer.value.play();
        }
      } else {
        if (audioPlayer.value) {
          audioPlayer.value.pause();
        }
      }
    }
  } else {
    handleDownloadClick(index);
  }
}

const downloadTime = ref([3000, 3500, 4000, 4500, 5000]);

const handleDownloadClick = (index) => {
  const item = musicData.value[index];
  let time = downloadTime.value[Math.floor(Math.random() * 5)];
  // console.log("downloadTime", time)
  if (item) {
    item.isLoading = true;

    // 模拟下载过程
    setTimeout(() => {
      // 下载完成
      item.isLoading = false;
      item.state = MUSIC_STATE.Downloaded;
      // alert('下载' + item.name + '完成');
      myMusicArray.push(item.id);
      musicData1.value.push(item);
      console.log(myMusicArray, musicData1);

      saveMyMusicArray();
      //下载完播放
      myMusicItemClick(musicData1.value.length - 1);
    }, time); // 假设下载需要2秒
  }
};

function myMusicItemClick(index) {
  isPlayingMusic.value = true;

  if (index != curMyMusicIndex.value) {
    curMyMusicIndex.value = index;
    curMusicId.value = musicData1.value[curMyMusicIndex.value].id;
    curMusicIndex.value = musicData1.value[curMyMusicIndex.value].id - 1;
    loadSong(musicData1.value[curMyMusicIndex.value]);
    setCurSongName();
  } else {
    if (isPlayingMusic.value == true) {
      if (audioPlayer.value) {
        audioPlayer.value.play();
      }
    } else {
      if (audioPlayer.value) {
        audioPlayer.value.pause();
      }
    }
  }
}

function deleteSong(deleteIndex) {
  if (IsOnlyOne.value) return;
  if (deleteIndex == curMyMusicIndex.value) {
    myMusicArray.splice(deleteIndex, 1);
    musicData1.value[deleteIndex].state = MUSIC_STATE.Not_Downloaded;
    musicData1.value.splice(deleteIndex, 1);
    console.log(myMusicArray, musicData1, musicData);
    saveMyMusicArray();

    if (musicData1.value.length > 0) {
      curMyMusicIndex.value = 0;
      curMusicId.value = musicData1.value[curMyMusicIndex.value].id;
      curMusicIndex.value = musicData1.value[curMyMusicIndex.value].id - 1;
      loadSong(musicData1.value[curMyMusicIndex.value]);
      setCurSongName();
    }
  } else {
    myMusicArray.splice(deleteIndex, 1);
    musicData1.value[deleteIndex].state = MUSIC_STATE.Not_Downloaded;
    musicData1.value.splice(deleteIndex, 1);
    console.log(myMusicArray, musicData1, musicData);
    saveMyMusicArray();
    //删完歌之后重新设置index
    curMyMusicIndex.value = musicData1.value.findIndex(
      (item) => item.id == curMusicId.value
    );
  }
}

function saveMyMusicArray() {
  // 将数组转换为 JSON 字符串
  const arrayString = JSON.stringify(myMusicArray);
  // 存储字符串到 LocalStorage
  localStorage.setItem("DownloadMusicArray", arrayString);

  musicNum.value = myMusicArray.length;

  IsOnlyOne.value = false;
  if (myMusicArray.length == 1) {
    IsOnlyOne.value = true;
  }
}

const loadSong = (song) => {
  audioPlayer.value.src = brazilImg + "/music/" + song.name + song.format;
  console.log(audioPlayer.value.src);
  audioPlayer.value.load();
  duration.value = audioPlayer.value.duration;
  currentTime.value = 0;
  progress.value = 0;
  // if (isPlayingMusic.value == true) {
  //   if (audioPlayer.value) {
  //     audioPlayer.value.play();
  //   }
  // }
};

function switchCycleMode() {
  if (curCycleMode.value < 3) {
    curCycleMode.value++;
  } else {
    curCycleMode.value = 1;
  }
}

function getCycleModeName(mode: number) {
  let name = "";
  switch (mode) {
    case CycleModeEnum.Sequential_Loop:
      name = "Ciclo";
      break;
    case CycleModeEnum.Random_Cycle:
      name = "Aleatório";
      break;
    case CycleModeEnum.Single_Loop:
      name = "Repetir";
      break;
  }
  return name;
}

function openMusicList() {
  showMusicPlayer.value = !showMusicPlayer.value;
}

const songName = ref("");

function setCurSongName() {
  songName.value = musicData1.value[curMyMusicIndex.value].name;
}

function truncatedText(text: string, length: number) {
  return text.length > length ? text.substring(0, length) + "..." : text;
}

const isHoveringDownload = ref(false); // 用于追踪鼠标是否悬停在按钮上
const isHoveringSupport = ref(false); // 用于追踪鼠标是否悬停在按钮上
const isHoveringHelp = ref(false); // 用于追踪鼠标是否悬停在按钮上

const musicNum = ref(1);

const SECLET_TYPE = readonly({
  System_Music: "0",
  My_Music: "1",
});

const MUSIC_STATE = readonly({
  Not_Downloaded: 0,
  Downloaded: 1,
});
const musicSecletType = ref(SECLET_TYPE.System_Music);

const tabData = ref([
  {
    label: "Sistema de Musica",
    value: SECLET_TYPE.System_Music,
  },
  {
    label: "Minhas musicas",
    value: SECLET_TYPE.My_Music,
  },
]);

const musicData = ref([
  {
    id: 1,
    name: "You Spin Me Round",
    size: "2M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 2,
    name: "Mariah Carey - Without You",
    size: "2M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 3,
    name: "Nicky Jam _ Will Smith _ Era Istrefi - Live It Up (Official Song 2018 FIFA World Cup Russia)",
    size: "3M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 4,
    name: "Sexy Love",
    size: "3M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 5,
    name: "Shakira - 6.Waka Waka",
    size: "2M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 6,
    name: "Shakira - 7.Try Everything (From Zootopia) [Official Music Video]",
    size: "3M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 7,
    name: "Shakira - 8.Whenever, Wherever (Official Music Video)",
    size: "3M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 8,
    name: "Shape of You",
    size: "4M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 9,
    name: "Sia-53.Chandelier",
    size: "937K",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 10,
    name: "Sia-54.Cheap Thrills",
    size: "3M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 11,
    name: "Sia-55.Dusk Till Dawn",
    size: "4M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 12,
    name: "Sia-55.Move Your Body",
    size: "4M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 13,
    name: "Silver Scrapes",
    size: "2M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 14,
    name: "Skin",
    size: "4M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 15,
    name: "Someone Like You",
    size: "",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 16,
    name: "Something Just Like This",
    size: "4M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 17,
    name: "Soviet March",
    size: "3M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 18,
    name: "Taylor Swift - 103.Blank Space",
    size: "4M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 19,
    name: "Taylor Swift - 104.You Belong With Me",
    size: "3M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 20,
    name: "Taylor Swift - 105.Shake It Off",
    size: "4M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 21,
    name: "Taylor Swift - 106.Bad Blood ft. Kendrick Lamar",
    size: "4M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 22,
    name: "That Girl",
    size: "3M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 23,
    name: "The Chainsmokers、Coldplay - 15.Something Just Like This",
    size: "4M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 24,
    name: "The Chainsmokers-67.Closer",
    size: "4M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 25,
    name: "The Chainsmokers-68.Don_t Let Me Down",
    size: "4M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 26,
    name: "The Chainsmokers-69.Paris",
    size: "3M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 27,
    name: "The Days",
    size: "4M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 28,
    name: "The Fox",
    size: "3M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 29,
    name: "The Nights(Remix)",
    size: "3M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 30,
    name: "The Phoenix",
    size: "937K",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 31,
    name: "Titanium",
    size: "4M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 32,
    name: "Toni Braxton - Yesterday",
    size: "3M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 33,
    name: "Victory",
    size: "4M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 34,
    name: "Wait Wait Wait",
    size: "3M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 35,
    name: "Waiting for Love",
    size: "4M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 36,
    name: "Will Smith-9.Live It Up",
    size: "3M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
  {
    id: 37,
    name: "Wiz Khalifa _ Charlie Puth - See You Again",
    size: "2M",
    state: MUSIC_STATE.Not_Downloaded,
    isLoading: false,
    format: ".mp3",
  },
]);

const musicData1 = ref([]);

const onTabChange = () => {
  if (musicSecletType.value == SECLET_TYPE.System_Music) {
    // scrollToSystemItem(curMusicIndex.value);
  } else if (musicSecletType.value == SECLET_TYPE.My_Music) {
    // scrollToMyItem(curMyMusicIndex.value);
  }
};

function downloadMusic(state: number) {
  return;
  showToast(state);
}

const scrollContainer = ref(null);
const systemRefs = ref([]);
const myRefs = ref([]);
const scrollToSystemItem = (index) => {
  if (!scrollContainer.value || !systemRefs.value[index]) return;
  showToast(index);

  scrollContainer.value.scrollTo({
    top: systemRefs.value[index].offsetTop,
    behavior: "smooth",
  });
};

const scrollToMyItem = (index) => {
  if (!scrollContainer.value || !myRefs.value[index]) return;
  showToast(index);

  scrollContainer.value.scrollTo({
    top: myRefs.value[index].offsetTop,
    behavior: "smooth",
  });
};

const enum JumpViewType {
  PROMOTION = 0,
  JUROS,
  VIP,
  REBATE,
  PENDENTE,
  HISTORY,
}

function jumpFunction(type: JumpViewType) {
  showLeftMenu.value = false;

  if (type == JumpViewType.PROMOTION) {
    secletType.value = 0;
    router.push("/advancement");
  } else if (type == JumpViewType.REBATE) {
    router.push({ path: "/advancement", query: { key: type } });
  } else if (type == JumpViewType.PENDENTE) {
    router.push({ path: "/advancement", query: { key: type } });
  } else if (type == JumpViewType.HISTORY) {
    router.push({ path: "/advancement", query: { key: type } });
  } else if (type == JumpViewType.JUROS) {
    router.push({ path: "/advancement", query: { key: type } });
  } else if (type == JumpViewType.VIP) {
    router.push({ path: "/advancement", query: { key: type } });
  }
}

const { run: runGetPlatformLinkData, data: platformLinkData } = useRequest(
  () => ApiGetPlatformLinkData(),
  {
    manual: true,
    onSuccess(res: any) {
      // console.log(res)
    },
  }
);
runGetPlatformLinkData();

const jumpUrl = (url: string) => {
  window.open(url, "_blank");
};
</script>

<template>
  <van-popup
    class="left"
    v-model:show="showLeftMenu"
    duration:0.3
    position="left"
    teleport="body"
    z-index="1001"
    :overlay-style="{
      transition: 'margin-top 0.5s ease 0s',
      marginTop: isShowAppBaixar ? 'var(--app-px-158)' : 'var(--app-px-88)',
    }"
    :style="{
      width: '40%',
      height: '100%',
      transition: 'margin-top 0.5s ease 0s',
      marginTop: isShowAppBaixar ? 'var(--app-px-158)' : 'var(--app-px-88)',
      paddingBottom: isShowAppBaixar ? 'var(--app-px-158)' : 'var(--app-px-88)',
    }"
  >
    <!-- {{ setMusicData() }}
    {{ setCurSongName() }} -->
    <section class="game-menu">
      <div v-for="(item, index) in menus" :key="item.id" :name="item.id">
        <div
          class="game-item"
          :class="{ active: clickId === item.id }"
          @click="liClick(item)"
        >
          <AppImage
            class="icon"
            :class="`icon_${10000}`"
            :src="`/icons/nav_${item.id}${
              clickId === item.id ? '-active' : ''
            }.png`"
            alt=""
          />
          <div class="text">{{ item.cateName }}</div>
        </div>
      </div>
    </section>
    <section class="aside-sys-menu">
      <!-- <div class="aside-music">
        <div class="headerMusicPlayer">
          <div class="control-buttons">
            <audio
              ref="audioPlayer"
              :src="audioSrc"
              @timeupdate="updateProgress"
              @loadedmetadata="updateTime"
              @ended="playNextSong"
            />
            <AppImage
              class="next"
              src="/img/leftMenu/music_previous_song.webp"
              alt=""
              @click="previousSong"
            />
            <AppImage
              :src="
                isPlayingMusic
                  ? '/img/leftMenu/music_pause.webp'
                  : '/img/leftMenu/music_play.webp'
              "
              alt=""
              @click="togglePlay"
            />
            <AppImage
              class="next"
              src="/img/leftMenu/music_next_song.webp"
              alt=""
              @click="nextSong"
            />
            <AppImage
              :src="`/img/leftMenu/music_cycle_mode${curCycleMode}.webp`"
              alt=""
              @click="switchCycleMode"
            />
            <div class="control-buttons-list" @click="openMusicList">
              <div class="music-number">
                <span> {{ musicNum }} </span>
              </div>
              <AppImage src="/img/leftMenu/music_number.webp" />
            </div>
          </div>
          <span class="songName">{{ truncatedText(songName, 28) }}</span>
        </div>
      </div> -->
      <div
        class="betting-record"
        @click="
          () => {
            router.push('/report');
            showLeftMenu = false;
          }
        "
      >
        <AppImage src="/img/leftMenu/betting_record_icon.webp" alt="" />
        <span>Apostas</span>
      </div>
      <div
        class="betting-record"
        @click="
          () => {
            router.push('/agent');
            showLeftMenu = false;
          }
        "
      >
        <AppImage src="/img/leftMenu/agent_icon.webp" alt="" />
        <span>Agente</span>
      </div>
      <div class="activity">
        <div class="title">
          <span>Promoção</span>
        </div>
        <div class="activity-item">
          <AppImage
            class="activity-item"
            src="/img/leftMenu/activity_1.webp"
            alt=""
            @click="jumpFunction(JumpViewType.PROMOTION)"
          />
          <span>Eventos</span>
        </div>
        <div class="activity-item">
          <AppImage
            class="activity-item"
            src="/img/leftMenu/activity_2.webp"
            alt=""
            @click="jumpFunction(JumpViewType.REBATE)"
          />
          <span>Rebate</span>
        </div>
        <div class="activity-item">
          <AppImage
            class="activity-item"
            src="/img/leftMenu/activity_3.webp"
            alt=""
            @click="jumpFunction(JumpViewType.PENDENTE)"
          />
          <span>Pendente</span>
        </div>
        <div class="activity-item">
          <AppImage
            class="activity-item"
            src="/img/leftMenu/activity_4.webp"
            alt=""
            @click="jumpFunction(JumpViewType.HISTORY)"
          />
          <span>Histórico</span>
        </div>
        <div class="activity-item">
          <AppImage
            class="activity-item"
            src="/img/leftMenu/activity_5.webp"
            alt=""
            @click="jumpFunction(JumpViewType.JUROS)"
          />
          <span>Poupança</span>
          <AppImage
            class="activity-item-tip"
            src="/img/leftMenu/qipao0.webp"
            alt=""
          />
          <div class="activity-item-tip-text">500,00%</div>
        </div>
        <div class="activity-item">
          <AppImage
            class="activity-item"
            src="/img/leftMenu/activity_6.webp"
            alt=""
            @click="jumpFunction(JumpViewType.VIP)"
          />
          <span>VIP</span>
        </div>
        <!-- <AppImage class="activity-item" src="/img/leftMenu/activity_1.webp" alt="" @click="jumpFunction(JumpViewType.PROMOTION)"/>
        <AppImage class="activity-item" src="/img/leftMenu/activity_2.webp" alt="" @click="jumpFunction(JumpViewType.REBATE)"/>
        <AppImage class="activity-item" src="/img/leftMenu/activity_3.webp" alt="" @click="jumpFunction(JumpViewType.PENDENTE)"/>
        <AppImage class="activity-item" src="/img/leftMenu/activity_4.webp" alt="" @click="jumpFunction(JumpViewType.HISTORY)"/> -->
        <!-- <div class="activity-item">
          <AppImage class="activity-item" src="/img/leftMenu/activity_5_1.webp" alt="" @click="jumpFunction(JumpViewType.JUROS)"/>
          <AppImage class="activity-item-tip" src="/img/leftMenu/qipao0.webp" alt="" />
          <div class="activity-item-tip-text">500,00%</div>
        </div> -->

        <!-- <AppImage class="activity-item" src="/img/leftMenu/activity_5.webp" alt="" @click="jumpFunction(JumpViewType.JUROS)"/> -->
        <!-- <AppImage class="activity-item" src="/img/leftMenu/activity_6.webp" alt="" @click="jumpFunction(JumpViewType.VIP)"/> -->
        <!-- <AppImage class="activity-item1" src="/img/leftMenu/activity_7.webp" alt="" /> -->
      </div>
      <div class="other-group">
        <div
          class="other-item"
          @mouseenter="isHoveringDownload = true"
          @mouseleave="isHoveringDownload = false"
          @click="
            () => {
              appStore.setIosInfoShow(true);
              showLeftMenu = false;
            }
          "
        >
          <!-- 使用v-if判断当前状态来显示图片 -->
          <!-- <AppImage v-if="!isHoveringDownload" src="/img/leftMenu/menu_download_normal.webp" alt="Regular State" />
          <AppImage v-if="isHoveringDownload" src="/img/leftMenu/menu_download_select.webp" alt="Hover State" /> -->
          <div>
            <svg
              t="1745825289922"
              class="icon"
              viewBox="0 0 1024 1024"
              version="1.1"
              xmlns="http://www.w3.org/2000/svg"
              p-id="85113"
              width="30"
              height="30"
            >
              <path
                d="M512 85.333333a42.666667 42.666667 0 0 1 42.666667 42.666667v409.002667l140.501333-140.501334a42.666667 42.666667 0 1 1 60.330667 60.330667l-213.333334 213.333333a42.666667 42.666667 0 0 1-60.330666 0l-213.333334-213.333333a42.666667 42.666667 0 0 1 60.330667-60.330667L469.333333 537.002667V128a42.666667 42.666667 0 0 1 42.666667-42.666667z"
                p-id="85114"
                fill="var(--svg-icon-color2)"
              ></path>
              <path
                d="M128 597.333333a42.666667 42.666667 0 0 1 42.666667 42.666667v51.2c0 36.565333 0 61.397333 1.621333 80.597333 1.493333 18.688 4.266667 28.288 7.68 34.944a85.333333 85.333333 0 0 0 37.290667 37.290667c6.656 3.413333 16.213333 6.186667 34.944 7.68C271.402667 853.333333 296.234667 853.333333 332.8 853.333333h358.4c36.565333 0 61.397333 0 80.597333-1.621333 18.688-1.493333 28.288-4.266667 34.944-7.68a85.333333 85.333333 0 0 0 37.290667-37.290667c3.413333-6.656 6.186667-16.213333 7.68-34.944 1.578667-19.2 1.621333-44.032 1.621333-80.597333V640a42.666667 42.666667 0 1 1 85.333334 0v52.949333c0 34.346667 0 62.72-1.877334 85.76-1.962667 24.021333-6.186667 46.08-16.725333 66.773334a170.666667 170.666667 0 0 1-74.581333 74.581333c-20.693333 10.538667-42.752 14.762667-66.730667 16.725333-23.082667 1.877333-51.456 1.877333-85.76 1.877334H331.008c-34.346667 0-62.72 0-85.76-1.877334-24.021333-1.962667-46.08-6.186667-66.773333-16.725333a170.666667 170.666667 0 0 1-74.581334-74.581333c-10.538667-20.693333-14.762667-42.752-16.725333-66.730667C85.333333 755.626667 85.333333 727.296 85.333333 692.992V640a42.666667 42.666667 0 0 1 42.666667-42.666667z"
                p-id="85115"
                fill="var(--svg-icon-color2)"
              ></path>
            </svg>
            <span class="other-item-text">Barxar App</span>
          </div>
        </div>
        <div
          class="other-item"
          @mouseenter="isHoveringSupport = true"
          @mouseleave="isHoveringSupport = false"
          @click="
            () => {
              router.push('/serviceMessages');
              showLeftMenu = false;
            }
          "
        >
          <!-- 使用v-if判断当前状态来显示图片 -->
          <!-- <AppImage
            v-if="!isHoveringSupport"
            src="/img/leftMenu/menu_support_normal.webp"
            alt="Regular State"
          />
          <AppImage
            v-if="isHoveringSupport"
            src="/img/leftMenu/menu_support_select.webp"
            alt="Hover State"
          /> -->
          <svg
            t="1745825511501"
            class="icon"
            viewBox="0 0 1024 1024"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            p-id="85362"
            width="30"
            height="30"
          >
            <path
              d="M526.165333 342.101333a53.248 53.248 0 0 0-59.264 34.816 42.666667 42.666667 0 1 1-80.469333-28.330666 138.581333 138.581333 0 0 1 269.312 46.208c0 54.613333-40.405333 89.685333-66.986667 107.392a267.989333 267.989333 0 0 1-56.234666 28.288l-1.194667 0.426666-0.426667 0.170667h-0.128l-0.085333 0.042667-13.525333-40.448 13.482666 40.490666a42.666667 42.666667 0 0 1-27.008-80.938666l0.298667-0.128 2.133333-0.810667a183.125333 183.125333 0 0 0 35.370667-18.133333c21.418667-14.250667 29.013333-27.093333 29.013333-36.394667V394.666667a53.248 53.248 0 0 0-44.288-52.565334zM475.733333 618.666667a42.666667 42.666667 0 0 1 42.666667-42.666667h0.426667a42.666667 42.666667 0 1 1 0 85.333333h-0.426667a42.666667 42.666667 0 0 1-42.666667-42.666666z"
              p-id="85363"
              fill="var(--svg-icon-color2)"
            ></path>
            <path
              d="M331.050667 85.333333h361.898666c34.346667 0 62.72 0 85.76 1.877334 24.021333 1.962667 46.08 6.186667 66.773334 16.725333a170.666667 170.666667 0 0 1 74.581333 74.581333c10.538667 20.693333 14.762667 42.752 16.725333 66.730667C938.666667 268.373333 938.666667 296.704 938.666667 331.008v246.442667c0 28.586667 0 52.181333-1.28 71.466666-1.408 20.053333-4.309333 38.570667-11.690667 56.405334a170.666667 170.666667 0 0 1-92.373333 92.373333c-17.834667 7.381333-36.352 10.282667-56.405334 11.648-19.285333 1.322667-42.88 1.322667-71.466666 1.322667H704c-22.912 0-27.434667 0.256-31.146667 1.152a42.666667 42.666667 0 0 0-17.493333 8.746666c-2.986667 2.432-5.888 5.930667-19.626667 24.234667l-63.018666 84.053333c-3.968 5.290667-8.490667 11.306667-12.8 16.128a64.938667 64.938667 0 0 1-24.746667 17.792 64 64 0 0 1-46.378667 0 64.938667 64.938667 0 0 1-24.704-17.792c-4.309333-4.821333-8.832-10.837333-12.8-16.170666L388.266667 844.8c-13.738667-18.346667-16.64-21.76-19.626667-24.234667a42.666667 42.666667 0 0 0-17.493333-8.746666c-3.712-0.853333-8.234667-1.152-31.146667-1.152h-1.450667c-28.586667 0-52.181333 0-71.509333-1.28-20.010667-1.408-38.528-4.309333-56.32-11.690667a170.666667 170.666667 0 0 1-92.416-92.373333c-7.381333-17.834667-10.282667-36.352-11.648-56.362667C85.333333 629.632 85.333333 606.037333 85.333333 577.450667V331.093333c0-34.346667 0-62.72 1.877334-85.76 1.962667-24.021333 6.186667-46.08 16.725333-66.773333a170.666667 170.666667 0 0 1 74.581333-74.581333c20.693333-10.538667 42.752-14.762667 66.730667-16.725334C268.373333 85.333333 296.704 85.333333 331.008 85.333333zM252.16 172.288c-18.688 1.493333-28.288 4.266667-34.944 7.68a85.333333 85.333333 0 0 0-37.290667 37.290667c-3.413333 6.656-6.186667 16.213333-7.68 34.944C170.666667 271.402667 170.666667 296.234667 170.666667 332.8v243.2c0 30.421333 0 51.072 1.109333 67.114667 1.066667 15.701333 2.986667 23.850667 5.376 29.525333a85.333333 85.333333 0 0 0 46.208 46.208c5.674667 2.346667 13.824 4.266667 29.525333 5.376 16.042667 1.109333 36.693333 1.109333 67.114667 1.109333h3.584c17.28 0 32.512 0 47.146667 3.413334a128 128 0 0 1 52.608 26.325333c11.52 9.642667 20.693333 21.802667 31.061333 35.669333l2.133333 2.858667 55.466667 73.941333 55.466667-73.941333 2.133333-2.858667c10.368-13.866667 19.498667-26.026667 31.061333-35.669333a128 128 0 0 1 52.608-26.325333c14.634667-3.413333 29.866667-3.413333 47.146667-3.413334h3.584c30.421333 0 51.072 0 67.114667-1.109333 15.701333-1.066667 23.850667-2.986667 29.525333-5.376a85.333333 85.333333 0 0 0 46.208-46.208c2.346667-5.674667 4.266667-13.824 5.376-29.525333 1.109333-16.042667 1.109333-36.693333 1.109333-67.114667V332.8c0-36.565333 0-61.397333-1.621333-80.597333-1.493333-18.688-4.266667-28.288-7.68-34.944a85.333333 85.333333 0 0 0-37.290667-37.290667c-6.656-3.413333-16.213333-6.186667-34.944-7.68C752.597333 170.666667 727.765333 170.666667 691.2 170.666667H332.8c-36.565333 0-61.397333 0-80.597333 1.621333z"
              p-id="85364"
              fill="var(--svg-icon-color2)"
            ></path>
          </svg>
          <span class="other-item-text">FAQ</span>
        </div>
        <div
          class="other-item"
          @mouseenter="isHoveringHelp = true"
          @mouseleave="isHoveringHelp = false"
          @click="
            () => {
              router.push('/serviceMessages');
              showLeftMenu = false;
            }
          "
        >
          <!-- 使用v-if判断当前状态来显示图片 -->
          <!-- <AppImage
            v-if="!isHoveringHelp"
            src="/img/leftMenu/menu_help_normal.webp"
            alt="Regular State"
          />
          <AppImage
            v-if="isHoveringHelp"
            src="/img/leftMenu/menu_help_select.webp"
            alt="Hover State"
          /> -->

          <svg
            t="1745825873716"
            class="icon"
            viewBox="0 0 1024 1024"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            p-id="98995"
            width="30"
            height="30"
          >
            <path
              d="M512 170.666667c86.357333 0 175.658667 55.125333 235.605333 133.205333A42.666667 42.666667 0 0 0 725.333333 341.333333v298.666667a42.666667 42.666667 0 0 0 11.349334 29.013333c-19.456 59.434667-45.653333 101.546667-80 130.218667C615.637333 833.493333 557.269333 853.333333 469.333333 853.333333a42.666667 42.666667 0 1 0 0 85.333334c100.608 0 180.864-22.826667 242.048-73.898667 57.6-48.128 93.056-116.906667 115.754667-200.661333l55.04-19.754667A85.333333 85.333333 0 0 0 938.666667 564.053333v-128.298666a85.333333 85.333333 0 0 0-44.885334-75.136l-25.728-13.866667 2.986667-1.322667C810.453333 208.341333 667.648 85.333333 512 85.333333 356.693333 85.333333 226.048 186.837333 165.162667 328.618667l-23.338667 8.362666A85.333333 85.333333 0 0 0 85.333333 417.28v128.298667a85.333333 85.333333 0 0 0 44.885334 75.136l105.557333 56.832A42.666667 42.666667 0 0 0 298.666667 640V341.333333a42.666667 42.666667 0 0 0-23.082667-37.888C330.965333 221.610667 417.408 170.666667 512 170.666667zM170.666667 417.28l42.666666-15.317333v166.613333l-42.666666-22.997333V417.28z m682.666666 146.773333l-42.666666 15.274667V412.8l42.666666 22.997333v128.298667z"
              p-id="98996"
              fill="var(--svg-icon-color2)"
            ></path>
          </svg>
          <span class="other-item-text">Suporte</span>
        </div>
      </div>
      <div class="offical">
        <span>Canal Oficial</span>
        <div
          class="offical-item"
          v-show="platformLinkData?.instagram.length > 0"
          @click="jumpUrl(platformLinkData.instagram)"
        >
          <AppImage src="/img/leftMenu/Instagram.webp" />
          <span>Instagram</span>
        </div>

        <div
          class="offical-item"
          v-show="platformLinkData?.telegram.length > 0"
          @click="jumpUrl(platformLinkData.telegram)"
        >
          <AppImage src="/img/leftMenu/Telegram.webp" />
          <span>Telegram</span>
        </div>

        <!-- <div class="link">
          <AppImage v-show="platformLinkData?.telegram.length > 0" src="/icons/telegram.webp" @click="jumpUrl(platformLinkData.telegram)"/>
          <AppImage v-show="platformLinkData?.facebook.length > 0" src="/icons/facebook.webp" @click="jumpUrl(platformLinkData.facebook)"/>
          <AppImage v-show="platformLinkData?.twitter.length > 0" src="/icons/twitter.webp" @click="jumpUrl(platformLinkData.twitter)" />
          <AppImage v-show="platformLinkData?.instagram.length > 0" src="/icons/instagram.webp" @click="jumpUrl(platformLinkData.instagram)" />
        </div> -->
        <!-- <div class="offical-item">
          <AppImage src="/img/leftMenu/Instagram.webp" />
          <span>Instagram</span>
        </div>
        <div class="offical-item">
          <AppImage src="/img/leftMenu/Instagram.webp" />
          <span>Instagram</span>
        </div>
        <div class="offical-item">
          <AppImage src="/img/leftMenu/Instagram.webp" />
          <span>Instagram</span>
        </div> -->
      </div>
      <div class="empty" />
    </section>
  </van-popup>

  <van-popup class="music-player-poup" v-model:show="showMusicPlayer" round>
    <div class="music-player">
      <span>Música</span>
      <div class="music-control">
        <span class="name">{{ truncatedText(songName, 60) }}</span>
        <div class="slider-content">
          <span class="time">{{ formatTime(currentTime) }}</span>
          <van-slider
            class="slider"
            v-model="progress"
            bar-height="5px"
            inactive-color="var(--theme-text-color-placeholder)"
            active-color="var(--theme-primary-color)"
            button-size="15px"
            @change="onChange"
          />
          <span class="time1">{{ durationTime }}</span>
        </div>
        <div class="operate-content">
          <div class="cycle-mode" @click="switchCycleMode">
            <AppImage
              class="img"
              :src="`/img/leftMenu/music_cycle_mode${curCycleMode}.webp`"
              alt=""
            />
            <span>{{ getCycleModeName(curCycleMode) }}</span>
          </div>
          <AppImage
            class="img1"
            src="/img/musicPlayer/music_previous_song.webp"
            alt=""
            @click="previousSong"
          />
          <AppImage
            class="img2"
            :src="
              isPlayingMusic
                ? '/img/musicPlayer/music_pause.webp'
                : '/img/musicPlayer/music_play.webp'
            "
            alt=""
            @click="togglePlay"
          />
          <AppImage
            class="img1"
            src="/img/musicPlayer/music_next_song.webp"
            alt=""
            @click="nextSong"
          />
          <div class="music-num">
            <span :style="{ fontSize: '15px' }">{{ musicNum }}</span>
            <span>Baixado</span>
          </div>
        </div>
      </div>
      <div class="music-list">
        <div class="music-list-content">
          <AppTab
            class="music-list-tab"
            :list-data="tabData"
            v-model="musicSecletType"
            @change="onTabChange"
          ></AppTab>
          <!-- <AppImage src="/img/musicPlayer/line.webp" alt="" /> -->
        </div>
        <div class="content" ref="scrollContainer">
          <div
            v-if="musicSecletType == SECLET_TYPE.System_Music"
            class="content-system"
          >
            <div
              class="music-item"
              v-for="(data, index) in musicData"
              :key="index"
              ref="systemRefs"
              @click="musicItemClick(index, data.state)"
            >
              <div v-if="curMusicIndex == index" class="music-info">
                <AppImage
                  :style="{ width: '14px', height: '16px' }"
                  src="/img/musicPlayer/music_icon.webp"
                  alt=""
                />
                <span class="child-style-4">{{
                  truncatedText(data.name, 31)
                }}</span>
                <span class="child-style-5">{{ data.size }}</span>
              </div>
              <div v-else class="music-info">
                <span class="child-style-1">{{ index + 1 }}</span>
                <span class="child-style-2">{{
                  truncatedText(data.name, 31)
                }}</span>
                <span class="child-style-3">{{ data.size }}</span>
              </div>
              <div class="music-state">
                <!-- <span>{{ data.state }}</span> -->
                <div v-if="data.isLoading" class="loader" />
                <div v-else>
                  <div v-if="data.state == 0">
                    <AppImage
                      class="state1"
                      src="/img/musicPlayer/music_download.webp"
                      alt=""
                      @click="downloadMusic(data.state)"
                    />
                  </div>
                  <div v-else>
                    <AppImage
                      class="state2"
                      src="/img/musicPlayer/music_gou.webp"
                      alt=""
                    />
                  </div>
                </div>
              </div>
              <!-- <span> {{ data.name }}</span> -->
            </div>
          </div>
          <div
            v-else-if="musicSecletType == SECLET_TYPE.My_Music"
            class="content-my"
          >
            <div
              class="music-item"
              v-for="(data, index) in musicData1"
              :key="index"
              ref="myRefs"
              @click="myMusicItemClick(index)"
            >
              <div v-if="curMusicId == data.id" class="music-info">
                <AppImage
                  :style="{ width: '14px', height: '16px' }"
                  src="/img/musicPlayer/music_icon.webp"
                  alt=""
                />
                <span class="child-style-4">{{
                  truncatedText(data.name, 28)
                }}</span>
                <span class="child-style-5">{{ data.size }}</span>
              </div>
              <div v-else class="music-info">
                <span class="child-style-1">{{ index + 1 }}</span>
                <span class="child-style-2">{{
                  truncatedText(data.name, 28)
                }}</span>
                <span class="child-style-3">{{ data.size }}</span>
              </div>
              <div class="music-operation">
                <div class="music-state">
                  <AppImage
                    class="state2"
                    src="/img/musicPlayer/music_gou.webp"
                    alt=""
                  />
                </div>
                <div class="delete">
                  <AppImage
                    :style="{ width: '15px', height: '15px' }"
                    src="/img/musicPlayer/music_delete.webp"
                    alt=""
                    @click.stop="() => deleteSong(index)"
                  />
                </div>
              </div>
              <!-- <span> {{ data.name }}</span> -->
            </div>
          </div>
        </div>
      </div>
    </div>
    <AppImage
      class="close-btn"
      src="/img/musicPlayer/music_close.webp"
      alt=""
      @click="
        () => {
          showMusicPlayer = false;
        }
      "
    />
  </van-popup>
</template>

<!-- <style lang="scss">
[theme='blue']:root {
  
}
</style> -->
<style lang="scss" scoped>
@import "../theme/mixin.scss";

.left {
  background-color: var(--theme-main-bg-color);
  // var(--app-navbar-height)
  margin-top: 20.95vw !important;
  overflow: auto;
  -ms-overflow-style: none;
  scrollbar-width: none;
  // transform: translateY(10%);
}

.left::-webkit-scrollbar {
  display: none;
}

.game-menu {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  position: relative;
  margin-top: 6px;

  .game-item {
    // @include webp('/img/leftMenu/item_unselecte');
    background-color: var(--theme-side-menu-btn-color);
    border-radius: 14px;
    background-size: 136px 96px;
    margin-left: 10px;
    margin-top: 10px;
    width: 136px;
    height: 96px;
    position: relative;
    text-align: center;
    display: flex;
    justify-content: center;
    /* 水平居中 */
    align-items: center;
    /* 垂直居中 */
    flex-direction: column;
    padding-top: 10px;

    .text {
      position: relative;
      width: 130px;
      height: 40px;
      line-height: 40px;
      text-align: center;
      font-size: 18px;
      color: var(--app-slots-text-color);
      color: var(--theme-side-menu-text-color);
      padding-top: 4px;
    }

    // &.active {
    //   // @include webp('/img/leftMenu/item_selecte');
    //   background-color: var(--theme-primary-color);
    //   .text {
    //     color: var(--theme-primary-font-color);
    //   }
    // }

    .icon_10000 {
      width: 60px;
      position: relative;
      // transform: translate(-50%,-50%);
      // left:50%;
      // top:50%;
      margin: 0 auto;
    }

    .icon_0 {
      top: 0px;
      width: 46px;
      height: 46px;
      position: relative;
    }

    .icon_1 {
      top: 0px;
      width: 42px;
      height: 42px;
      position: relative;
    }

    .icon_2 {
      top: 0px;
      width: 42px;
      height: 42px;
      position: relative;
    }

    .icon_3 {
      top: 0px;
      width: 44px;
      height: 44px;
      position: relative;
    }

    .icon_7 {
      top: 0px;
      width: 42px;
      height: 42px;
      position: relative;
    }

    .icon_8 {
      top: 0px;
      width: 44px;
      height: 44px;
      position: relative;
    }

    .icon_100 {
      top: 0px;
      width: 44px;
      height: 44px;
      position: relative;
    }
  }
}

.aside-sys-menu {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  // margin-top: 10px;
  flex-direction: column;

  .aside-music {
    width: 280px;
    height: 76px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    .headerMusicPlayer {
      // @include webp('/img/leftMenu/music_player_bg');
      background-color: var(--theme-side-menu-btn-color);
      border-radius: 10px;
      width: 280px;
      height: 76px;
      background-size: 280px 76px;
      display: flex;
      flex-direction: column;
      position: relative;
      align-items: center;

      // justify-content: center;
      .control-buttons {
        left: -10px;
        width: 100%;
        padding-top: 8px;
        margin-top: 5px;
        margin-bottom: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;

        img {
          position: relative;
          margin-left: 24px;
          width: 30px;
          /* 根据需要调整 */
          // height: 30px;
          /* 根据需要调整 */
        }

        &-list {
          margin-left: 30px;
          @include webp("/img/leftMenu/music_list");
          background-size: 32px 26px;
          width: 32px;
          height: 26px;

          .music-number {
            @include webp("/img/leftMenu/music_number");
            background-size: 29px 21px;
            width: 29px;
            height: 21px;
            text-align: center;
            display: flex;
            justify-content: center;
            /* 水平居中 */
            align-items: center;
            position: absolute;
            top: -6px;
            left: 212px;
          }

          span {
            color: var(--theme-text-color);
            font-size: 16px;
          }
        }
        .next {
          width: 20px;
          // height: 20px;
        }
      }

      // .control-buttons img {
      //   position: relative;
      //   margin-left: 20px;
      //   width: 30px;
      //   /* 根据需要调整 */
      //   height: 30px;
      //   /* 根据需要调整 */
      // }

      .songName {
        color: var(--theme-side-menu-text-color);
        font-size: 18px;
      }
    }
  }

  .betting-record {
    // @include webp('/img/leftMenu/betting_record_bg');
    background-color: var(--theme-side-menu-btn-color);
    border-radius: 10px;
    width: 280px;
    height: 80px;
    background-size: 280px 80px;
    display: flex;
    flex-direction: row;
    position: relative;
    align-items: center;
    margin-top: 10px;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding-left: 20px;

    img {
      width: 35px;
      height: 36px;
      position: relative;
      // left:-100px;
    }

    span {
      width: 190px;
      color: var(--theme-side-menu-text-color);
      font-size: 24px;
    }
  }

  .activity {
    // @include webp('/img/leftMenu/activity_bg');
    background-color: var(--theme-side-menu-btn-color);
    border-radius: 10px;
    width: 280px;
    height: 340px;
    background-size: 280px 340px;
    margin-top: 10px;
    padding-top: 10px;
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    position: relative;
    .title {
      width: 280px;
      height: 32px;
      text-align: center;
      span {
        color: var(--theme-side-menu-text-color);
        font-size: 24px;
      }
    }
    .activity-item {
      margin-left: 10px;
      margin-right: 1px;
      margin-top: 2px;
      position: relative;
      text-align: center;
      display: flex;
      justify-content: center;
      /* 水平居中 */
      align-items: center;
      /* 垂直居中 */
      flex-direction: column;
      width: 120px;
      height: 80px;

      span {
        position: absolute;
        font-size: 15px;
        left: 10px;
        top: 12px;
        color: #fff;
        text-shadow: 0px 0px 0 #999999, 0px 0px 0 #999999, 0px 0px 0 #999999,
          1px 1px 0 #999999;
      }
    }
    .activity-item-tip {
      position: absolute;
      left: 53px;
      top: -16px;
      width: 83px;
      height: 26px;
    }
    .activity-item-tip-text {
      position: absolute;
      left: 53px;
      top: -16px;
      width: 83px;
      height: 26px;
      color: rgb(255, 255, 0);
      font-size: 18px;
    }
    .activity-item1 {
      margin-left: 10px;
      margin-right: 1px;
      margin-top: 0px;
      position: relative;
      text-align: center;
      display: flex;
      justify-content: center;
      /* 水平居中 */
      align-items: center;
      /* 垂直居中 */
      flex-direction: column;
      width: 260px;
      height: 82px;
    }
  }

  .other-group {
    width: 270px;
    height: 180px;
    padding-top: 10px;
    padding-left: 20px;

    .other-item {
      width: 270px;
      height: 60px;
      display: flex;
      align-items: center;
      
      div {
        display: flex;
        align-items: center;
      }

      .icon {
        width: 30px;
        height: 30px;
        margin-right: 15px;  // Fixed margin for all icons
      }

      .other-item-text {
        color: black;
        font-size: 20px;
        color: var(--theme-side-menu-text-color);
      }

      img {
        width: 200px;
        height: 60px;
      }
    }
  }

  .offical {
    width: 270px;
    padding-top: 20px;

    span {
      color: var(--theme-text-color-lighten);
      font-size: 24px;
      padding-left: 6px;
    }

    .offical-item {
      // @include webp('/img/leftMenu/offical_bg');
      // border-radius:20px;
      background-color: var(--theme-side-menu-btn-color);
      border-radius: 10px;
      background-size: 270px 52px;
      background-repeat: no-repeat;
      width: 270px;
      height: 52px;
      margin-top: 12px;
      display: flex;
      // justify-content: center;
      /* 水平居中 */
      align-items: center;
      /* 垂直居中 */
      flex-direction: row;

      padding-left: 16px;

      img {
        width: 36px;
        height: 36px;
      }

      span {
        color: var(--theme-text-color-darken);
        font-size: 20px;
        font-weight: 400;
      }
    }
  }

  .empty {
    width: 280px;
    height: 30px;
  }
}

.music-player-poup {
  width: 690px;
  height: 1200px;
  display: flex;
  // align-items: center;
  // flex-direction: column;
  position: absolute;
  justify-content: center;

  .close-btn {
    position: absolute;
    width: 56px;
    height: 56px;
    top: 95%;
  }
}

.music-player {
  width: 680px;
  height: 980px;
  background-color: var(--theme-bg-color);
  background-size: 690px 980px;
  text-align: center;
  position: absolute;
  display: flex;
  // justify-content: top;
  align-items: center;
  /* 垂直居中 */
  flex-direction: column;
  padding-top: 20px;
  border-radius: 20px;
  border: 2px solid var(--theme-color-line);
  top: 8%;

  span {
    position: relative;
    color: var(--theme-text-color-darken);
    font-size: 28px;
  }

  .music-control {
    margin-top: 20px;
    position: relative;
    width: 588px;
    height: 224px;
    background-color: var(--theme-main-bg-color);
    border-radius: 16px;
    display: flex;
    justify-content: top;
    align-items: center;
    /* 垂直居中 */
    flex-direction: column;

    .name {
      padding-top: 22px;
      position: relative;
      color: var(--theme-text-color-darken);
      font-size: 21px;
    }

    .slider-content {
      margin-top: 26px;
      // padding-top: 40px;
      position: relative;
      width: 588px;
      height: 30px;
      display: flex;
      justify-content: center;
      align-items: center;
      /* 垂直居中 */
      flex-direction: row;

      .slider {
        width: 300px;
      }

      .time {
        padding-right: 50px;
        color: var(--theme-text-color);
        font-size: 20px;
      }

      .time1 {
        padding-left: 50px;
        color: var(--theme-text-color);
        font-size: 20px;
      }
    }

    .operate-content {
      margin-top: 26px;
      position: relative;
      width: 588px;
      height: 80px;
      display: flex;
      flex-direction: row;
      /* 水平布局 */
      justify-content: space-around;
      /* 子节点之间的间距平均分布 */
      align-items: center;

      /* 垂直居中对齐 */
      .cycle-mode {
        width: 100px;
        display: flex;
        justify-content: center;
        align-items: center;
        /* 垂直居中 */
        flex-direction: column;

        span {
          font-size: 22px;
          top: 0px;
        }
      }

      .music-num {
        display: flex;
        justify-content: center;
        align-items: center;
        /* 垂直居中 */
        flex-direction: column;

        span {
          color: var(--theme-text-color-lighten);
          font-size: 22px;
        }
      }

      .img {
        width: 32px;
        // height: 28px;
      }

      .img1 {
        width: 56px;
        height: 56px;
      }

      .img2 {
        width: 76px;
        height: 76px;
      }
    }
  }

  .music-list {
    margin-top: 20px;
    position: relative;
    width: 588px;
    height: 610px;
    background-color: var(--theme-main-bg-color);
    border-radius: 16px;
    display: flex;
    justify-content: top;
    align-items: center;
    /* 垂直居中 */
    flex-direction: column;

    .music-list-content {
      width: 588px;
      height: 40px;

      .music-list-tab {
        width: 588px;

        position: absolute;
        top: 0px;
        border-bottom: thin solid var(--theme-color-line);
      }

      img {
        position: absolute;
        width: 588px;
        height: 2px;
        top: 69px;
        left: 0px;
      }
    }

    .content {
      // padding-top: 36px;
      margin-top: 36px;
      width: 588px;
      height: 524px;
      position: relative;
      display: flex;
      justify-content: top;
      align-items: center;
      /* 垂直居中 */
      flex-direction: column;
      overflow-y: auto;
      -ms-overflow-style: none;
      scrollbar-width: none;

      .content::-webkit-scrollbar {
        display: none;
      }

      .content-system {
        // width: 588px;
        // height: 524px;
        width: 100%;
        position: relative;
        display: flex;
        justify-content: top;
        align-items: center;
        /* 垂直居中 */
        flex-direction: column;

        .music-item {
          box-sizing: border-box;
          width: 560px;
          height: 80px;
          position: relative;
          list-style-type: none;
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-bottom: thin solid var(--theme-color-line);
          /* 子节点之间的分隔线 */
          // margin-top: 30px;
          // margin-bottom: 30px;

          .music-info,
          .music-state {
            display: flex;
            align-items: center;
          }

          .music-info {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            flex-direction: row;

            img {
              margin-left: 10px;
            }

            .child-style-1 {
              padding-left: 10px;
              /* 子节点1样式 */
              color: var(--theme-text-color-lighten);
              font-size: 22px;
            }

            .child-style-2 {
              padding-left: 10px;
              /* 子节点2样式 */
              color: var(--theme-text-color-darken);
              font-size: 24px;
            }

            .child-style-3 {
              padding-left: 10px;
              /* 子节点3样式 */
              color: var(--theme-text-color-lighten);
              font-size: 20px;
            }

            .child-style-4 {
              padding-left: 12px;
              /* 子节点3样式 */
              color: var(--theme-primary-color);
              font-size: 24px;
            }

            .child-style-5 {
              padding-left: 12px;
              /* 子节点3样式 */
              color: var(--theme-text-color-lighten);
              font-size: 22px;
            }
          }

          .music-state {
            justify-content: flex-end;

            .state1 {
              width: 32px;
              height: 32px;
            }

            .state2 {
              width: 30px;
              height: 22px;
            }

            .loader {
              /* 加载指示器样式 */
              border: 4px solid #f3f3f3;
              border-top: 4px solid #3498db;
              border-radius: 50%;
              width: 20px;
              height: 20px;
              animation: spin 2s linear infinite;
              margin-right: 5px;
            }

            @keyframes spin {
              0% {
                transform: rotate(0deg);
              }

              100% {
                transform: rotate(360deg);
              }
            }
          }
        }
      }

      .content-my {
        // width: 588px;
        // height: 524px;
        width: 100%;
        position: relative;
        display: flex;
        justify-content: top;
        align-items: center;
        /* 垂直居中 */
        flex-direction: column;

        .music-item {
          box-sizing: border-box;
          width: 560px;
          height: 80px;
          position: relative;
          list-style-type: none;
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-bottom: thin solid var(--theme-color-line);
          /* 子节点之间的分隔线 */
          // margin-top: 30px;
          // margin-bottom: 30px;

          .music-info,
          .music-operation {
            display: flex;
            align-items: center;
          }

          .music-info {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            flex-direction: row;

            img {
              margin-left: 10px;
            }

            .child-style-1 {
              padding-left: 10px;
              /* 子节点1样式 */
              color: var(--theme-text-color-lighten);
              font-size: 22px;
            }

            .child-style-2 {
              padding-left: 10px;
              /* 子节点2样式 */
              color: var(--theme-text-color-darken);
              font-size: 24px;
            }

            .child-style-3 {
              padding-left: 10px;
              /* 子节点3样式 */
              color: var(--theme-text-color-lighten);
              font-size: 20px;
            }

            .child-style-4 {
              padding-left: 12px;
              /* 子节点3样式 */
              color: var(--theme-primary-color);
              font-size: 24px;
            }

            .child-style-5 {
              padding-left: 12px;
              /* 子节点3样式 */
              color: var(--theme-text-color-lighten);
              font-size: 22px;
            }
          }

          .music-operation {
            img {
              margin-right: 10px;
              margin-left: 6px;
            }

            .music-state {
              justify-content: flex-end;

              .state1 {
                width: 32px;
                height: 32px;
              }

              .state2 {
                width: 30px;
                height: 22px;
              }
            }
          }
        }
      }
    }
  }
}
</style>
