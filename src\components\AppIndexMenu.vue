<script setup lang="ts" name="IndexMenu">
const router = useRouter();

const appStore = useAppStore();
const { isLogin, appFooterVisible, secletType, userInfo, isIOS } =
  storeToRefs(appStore);
const { isIos } = getBrowser();
const dragStore = useDragStore();
const { showChatDrag } = storeToRefs(dragStore);
import checkIcon from "/icons/svg/check.svg?raw";
import supportIcon from "/icons/svg/support.svg?raw";
import ticketIcon from "/icons/svg/ticket.svg?raw";
import pigBankIcon from "/icons/svg/pigBank.svg?raw";
import searchItemIcon from "/icons/svg/searchItem.svg?raw";
import wallet02Icon from "/icons/svg/wallet02.svg?raw";
import writeIcon from "/icons/svg/write.svg?raw";
import shellIcon from "/icons/svg/shell.svg?raw";
import menu02Icon from "/icons/svg/menu02.svg?raw";
import moneyShareIcon from "/icons/svg/moneyShare.svg?raw";
import onlineIcon from "/icons/svg/online.svg?raw";
import gift03Icon from "/icons/svg/gift03.svg?raw";
import giveMoneyIcon from "/icons/svg/giveMoney.svg?raw";
import itemIcon from "/icons/svg/item.svg?raw";
import phoneIcon from "/icons/svg/Phone.svg?raw";
import checkMenuIcon from "/icons/svg/checkMenu.svg?raw";
import coinIcon from "/icons/svg/coin.svg?raw";
import controllerIcon from "/icons/svg/controller.svg?raw";
import crownIcon from "/icons/svg/crown.svg?raw";
import gift02Icon from "/icons/svg/gift02.svg?raw";
import cardConfigIcon from "/icons/svg/cardConfig.svg?raw";
import editPersonIcon from "/icons/svg/editPerson.svg?raw";
import gift01Icon from "/icons/svg/gift01.svg?raw";
import { setScrollTop } from "vant/lib/utils";

import instagramIcon from "/icons/svg/instagram.svg?raw"
import telegramIcon from "/icons/svg/telegram.svg?raw"

enum Menus {
  // HOME = 'index',
  // PROMOTION = 'promotion',
  // DEPOSIT = 'finance-deposit',
  VIP = "vip",
  INICIO = "inicio",
  PROMOGAO = "promogao",
  DEPOSIT = "deposit",
  CONVIDAR = "convidar",
  CONTA = "conta",

  SUPORTE = "suporte",
  SAQUE = "saque",
  MENU = "menu",
  BAIXAR = "baixar",
}

const saqueAref = ref();
const menuAref = ref();
const depositAref = ref();
const inicioAref = ref();
const contaAref = ref();
const convidarAref = ref();
const oldSelref = ref();
const baixarAref = ref();

const enum JumpViewType {
  Suporte = 0,
  Noticia,
  Notificacao,
  PainelRolante,
  BonusDeSugestao,
}

const menus = ref([
  { path: "/", name: Menus.INICIO, eleref: inicioAref, auth: false },
  {
    path: "/advancement",
    name: Menus.PROMOGAO,
    eleref: inicioAref,
    auth: false,
  },
  { path: "/finance", name: Menus.DEPOSIT, eleref: depositAref, auth: true },
  {
    path: "/agent/?tab=0",
    name: Menus.CONVIDAR,
    eleref: convidarAref,
    auth: true,
  },
  { path: "/personal", name: Menus.CONTA, eleref: contaAref, auth: true },

  { path: "/takeout", name: Menus.SAQUE, eleref: saqueAref, auth: false },
  { path: "/menu", name: Menus.MENU, eleref: menuAref, auth: false },
  {
    path: "/serviceMessages",
    name: Menus.SUPORTE,
    eleref: inicioAref,
    auth: false,
  },
  { path: "/mensagens", name: Menus.BAIXAR, eleref: baixarAref, auth: false },
  {
    path: "/advancement?key=vip",
    name: Menus.VIP,
    eleref: inicioAref,
    auth: true,
  },
]);

const activeMenu = ref();

const toggleAni = function (curref: any) {
  // let beat = curref.classList.contains('ani-beat')
  // if (beat) {
  //   curref.classList.remove('ani-beat')
  //   setTimeout(() => {
  //     curref.classList.add('ani-beat')
  //   }, 0);
  // } else {
  //   curref.classList.add('ani-beat')
  // }
};

const goPay = () => {
  appStore.setShowRecharge(false);
  appStore.setPayVisble(true);
};

const menuAClick = function (ty: Menus) {
  if (ty == Menus.DEPOSIT) {
    if (isLogin.value) {
      appStore.setPayVisble(true);
    } else {
      appStore.setLoginDialogVisible(true);
    }
    return;
  } else if (ty == Menus.BAIXAR) {
    if (isIos) {
      //showAddToDesktop.value = true;
    } else {
      // openUrl(downloadUrl.value?.url)
      if (window.installButtonclick) {
        installButtonclick();
      }
    }
    return;
  }
  let routerPush = (menu: Menus) => {
    const item = menus.value.filter((i) => i.name === menu)[0];
    if (item) {
      if (!item.auth || isLogin.value) {
        activeMenu.value = menu;
        if (item.name !== Menus.DEPOSIT) {
          toggleAni(item.eleref);
        }
      }
      if (item.name == Menus.PROMOGAO) {
        secletType.value = 0;
      }
      if (item.name == Menus.DEPOSIT) {
        goPay();
      } else {
        // if (item.auth && !isLogin)
        router.push(item.path);
      }
    }
  };

  // 关闭menu，返回主页
  if (activeMenu.value === ty && ty === "menu") {
    // router.push('/')
    if (!oldSelref.value) {
      router.push("/");
    } else {
      activeMenu.value = oldSelref.value;
      routerPush(activeMenu.value);
    }
    return;
  } else if (ty !== "menu") {
    const item = menus.value.filter((i) => i.name === ty)[0];
    if (item) {
      if (!item.auth || isLogin.value) {
        oldSelref.value = ty;
      }
    }
  }
  routerPush(ty);
};

// 刷新金额
const loading = ref(false);
const refreshBalance = () => {
  if (loading.value) {
    return;
  }

  loading.value = true;
  appStore.runGetMemberInfo();
  setTimeout(() => {
    loading.value = false;
  }, 1500);
};

const copyName = () => {
  copy(userInfo.value.username || "");
  showToast("Copied!");
};

const isOpenMenu = ref(false);

const { run: runGetPlatformLinkData, data: platformLinkData } = useRequest(
  () => ApiGetPlatformLinkData(),
  {
    manual: true,
    onSuccess(res: any) {
      // console.log(res)
    },
  }
);
runGetPlatformLinkData();

const jumpUrl = (url: string) => {
  window.open(url, "_blank");
};

const userAgent = navigator.userAgent || navigator.vendor;
// const isIOS = ref(false)

function gotoDownLoad() {
  appStore.setIsShowAppBaixar(false);

  if (
    userAgent.indexOf("iPad") > -1 ||
    userAgent.indexOf("iPhone") > -1 ||
    userAgent.indexOf("iPod") > -1
  ) {
    isIOS.value = true; //'iOS'; // 苹果浏览器
  } else if (userAgent.indexOf("Android") > -1) {
    if (window.installButtonclick) {
      installButtonclick();
    }
  } else {
    //  show.value= false // 'unknown'; // 其他浏览器或平台
    if (window.installButtonclick) {
      installButtonclick();
    }
  }
}
</script>
<template>
  <div class="IndexMenu">
    <div class="loginDiv" v-if="!isLogin">
      <div class="content">
        <button class="login-btn" @click="() => openLoginDialog(true)">
          Login
        </button>
        <button class="register-btn" @click="() => openRegisterDialog(true)">
          Registro
        </button>
      </div>
    </div>

    <div class="loginDiv" v-if="isLogin">
      <div class="content">
        <div class="name">
          <div
            class="_vip-badge-box_1cnxd_30"
            style="background: rgb(36, 178, 153)"
          >
            <img
              class="lobby-image lobby-image _img_1cnxd_39"
              data-load-strategy="loading-set@1:lazy@0"
              data-status="success"
              data-thumb="0"
              data-id="71596268-79cf-4c5e-89ec-f9cb579b2aa1"
              src="https://2akg.2aapi.com/siteadmin/skin/lobby_asset/common/common/profile/img_vip_v.avif?manualVersion=1&amp;version=dccf2cd2a0"
              alt="."
            /><span data-text="0" class="_level_1cnxd_45">0</span>
          </div>
          <span v-if="userInfo.real_name !== ''">{{ userInfo.real_name }}</span>

          <span v-else>{{ userInfo.username }}</span>
          <!-- <AppImage
            class="copy"
            src="/icons/personal_copy.webp"
            :width="24"
            @click="() => copyName()"
          /> -->
          <div class="copy" @click="() => copyName()">
            <svg
              width="1em"
              height="1em"
              fill="var(--svg-icon-color)"
              class=""
              viewBox="0 0 20.727 24"
            >
              <path
                id="4e0f559826fd31bb0ad8a4c5934c2092-comm_icon_copy"
                d="M1801.586,16.829a1.094,1.094,0,0,1-1.1-1.093V-1.781a1.094,1.094,0,0,1,1.1-1.093,1.094,1.094,0,0,1,1.095,1.093V14.643h13.136a1.093,1.093,0,1,1,0,2.185Zm4.305-4.3a1.1,1.1,0,0,1-1.1-1.093V-6.078a1.094,1.094,0,0,1,1.1-1.093h8.759a1.094,1.094,0,0,1,.773.32l5.474,5.463a1.091,1.091,0,0,1,.321.773V11.439a1.094,1.094,0,0,1-1.1,1.093Zm1.094-2.185h12.044V1.569h-5.474A1.094,1.094,0,0,1,1812.46.476V-4.986h-5.475Z"
                transform="translate(-1800.491 7.171)"
              ></path>
            </svg>
          </div>
        </div>
        <div class="right-balance">
          <AppImage class="right-logo" src="icons/content.webp" alt="" />
          <label class="right-text" @click="appStore.setPayVisble(true)">
            {{ userInfo.formatAmount }}</label
          >
          <div
            class="right-refresh"
            :class="{ rotate: loading }"
            @click="refreshBalance"
          ></div>
        </div>
      </div>
    </div>

    <div class="menuDiv">
      <div class="menu-btn">
        <!-- <AppImage
          src="/img/homeTab/agent_promote.avif"
          :width="57"
          
        /> -->
        <div
          @click="() => menuAClick(Menus.CONVIDAR)"
          class="svgIcon"
          v-html="moneyShareIcon"
        ></div>
        <span>Agente</span>
      </div>
      <!-- <div class="menu-btn" @click="() => menuAClick(Menus.DEPOSIT)">
        <AppImage src="/img/homeTab/deposit" :width="57"  />
        <span>Depósito</span>
      </div> -->
      <div class="menu-btn" @click="() => gotoDownLoad()">
        <!-- <AppImage src="/img/homeTab/app_download.avif" :width="57" /> -->
        <div class="svgIcon" v-html="phoneIcon"></div>
        <span>Baixar App</span>
      </div>
      <div class="menu-btn" @click="() => menuAClick(Menus.SUPORTE)">
        <!-- <AppImage src="/img/homeTab/vip.avif" :width="57" /> -->
        <div class="svgIcon" v-html="supportIcon"></div>
        <span>Suporte</span>
      </div>

      <div class="menu-btn" @click="isOpenMenu = !isOpenMenu">
        <!-- <AppImage src="/img/homeTab/more_menu.avif" :width="57" /> -->
        <div class="svgIcon" v-html="itemIcon"></div>
        <span>Mais</span>
      </div>
    </div>

    <div
      class="overlay"
      v-if="isOpenMenu && showChatDrag"
      @click="isOpenMenu = !isOpenMenu"
    ></div>

    <div
      class="menuPoup"
      v-if="isOpenMenu && showChatDrag"
      @click="isOpenMenu = !isOpenMenu"
    >
      <div class="menuLayout">
        <div class="menuItem" @click.stop="() => menuAClick(Menus.VIP)">
          <!-- <AppImage src="/img/homeTab/customer_support.avif" :width="57" /> -->
          <div class="svgIcon" v-html="crownIcon"></div>
          <span>VIP</span>
        </div>
        <div class="menuItem" @click.stop="() => menuAClick(Menus.SAQUE)">
          <!-- <AppImage src="/img/homeTab/withdraw_money.avif" :width="57" /> -->
          <div class="svgIcon" v-html="ticketIcon"></div>
          <span>Saque</span>
        </div>
        <div class="menuItem" @click.stop="() => menuAClick(Menus.PROMOGAO)">
          <!-- <AppImage src="/img/homeTab/promotions.avif" :width="57" /> -->
          <div class="svgIcon" v-html="gift02Icon"></div>
          <span>Eventos</span>
        </div>
        <div
          class="menuItem"
          @click.stop="
            () =>
              router.push({
                path: '/serviceMessages',
                query: { key: JumpViewType.BonusDeSugestao },
              })
          "
        >
          <!-- <AppImage src="/img/homeTab/referral_bonus.avif" :width="57" /> -->
          <div class="svgIcon" v-html="writeIcon"></div>
          <span>Bônus de Sugestão</span>
        </div>
        <div
          class="menuItem"
          @click.stop="() => router.push('/personalCenter/security')"
        >
          <!-- <AppImage src="/img/homeTab/security_settings.avif" :width="57" /> -->
          <div class="svgIcon" v-html="shellIcon"></div>
          <span>Segurança</span>
        </div>
        <div
          class="menuItem"
          @click.stop="() => router.push('/personalCenter/userInfo')"
        >
          <!-- <AppImage src="/img/homeTab/dados.avif" :width="57" /> -->
          <div class="svgIcon" v-html="editPersonIcon"></div>
          <span>Dados</span>
        </div>
        <div
          class="menuItem"
          @click.stop="
            () => router.push({ path: '/report', query: { key: 0 } })
          "
        >
          <!-- <AppImage src="/img/homeTab/account_info.avif" :width="57" /> -->
          <div class="svgIcon" v-html="menu02Icon"></div>
          <span>Conta</span>
        </div>
        <div
          class="menuItem"
          @click.stop="
            () => router.push({ path: '/report', query: { key: 1 } })
          "
        >
          <!-- <AppImage src="/img/homeTab/apostas.avif" :width="57" /> -->
          <div class="svgIcon" v-html="controllerIcon"></div>
          <span>Apostas</span>
        </div>
        <div
          class="menuItem"
          @click.stop="
            () => router.push({ path: '/report', query: { key: 2 } })
          "
        >
          <!-- <AppImage src="/img/homeTab/transaction_report.avif" :width="57" /> -->
          <div class="svgIcon" v-html="checkIcon"></div>
          <span>Relatório</span>
        </div>
        <div
          class="menuItem"
          @click.stop="
            () => router.push({ path: '/advancement', query: { key: 4 } })
          "
        >
          <!-- <AppImage src="/img/homeTab/pending_tasks.avif" :width="57" /> -->
          <div class="svgIcon" v-html="checkIcon"></div>
          <span>Pendente</span>
        </div>
        <div
          class="menuItem"
          @click.stop="
            () => router.push({ path: '/takeout', query: { key: 2 } })
          "
        >
          <!-- <AppImage src="/img/homeTab/withdraw_management.avif" :width="57" /> -->
          <div class="svgIcon" v-html="cardConfigIcon"></div>
          <span>Gestão Retiradas</span>
        </div>
        <div
          class="menuItem"
          @click.stop="
            () => router.push({ path: '/takeout', query: { key: 1 } })
          "
        >
          <!-- <AppImage src="/img/homeTab/betting_tasks.avif" :width="57" /> -->
          <div class="svgIcon" v-html="checkIcon"></div>
          <span>Tarefas de Apostas</span>
        </div>
        <div
          class="menuItem"
          @click.stop="
            () => router.push({ path: '/advancement', query: { key: 5 } })
          "
        >
          <!-- <AppImage src="/img/homeTab/Histórico.avif" :width="57" /> -->
          <div class="svgIcon" v-html="gift03Icon"></div>
          <span>Histórico</span>
        </div>
        <div
          class="menuItem"
          v-show="platformLinkData?.instagram.length > 0"
          @click.stop="jumpUrl(platformLinkData.instagram)"
        >
          <!-- <AppImage src="/img/leftMenu/Instagram.webp" :width="57" /> -->
          <div class="svgIcon" v-html="instagramIcon"></div>
          <span>Instagram</span>
        </div>
        <div
          class="menuItem"
          v-show="platformLinkData?.telegram.length > 0"
          @click.stop="jumpUrl(platformLinkData.telegram)"
        >
          <!-- <AppImage src="/img/leftMenu/Telegram.webp" :width="57" /> -->
          <div class="svgIcon" v-html="telegramIcon"></div>
          <span>Telegram</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
._vip-badge-box_1cnxd_30 {
  width: 40px;
  height: 20px;
  border-radius: 5px 3px;
  margin-right: 10px;
}
._img_1cnxd_39 {
  width: 15px;
  height: 18px;
  margin-bottom: 4px;
  margin-left: 5px;
}

._level_1cnxd_45 {
  // margin-left: 0px;

  left: -1px;
  bottom: 7px;
  font-size: 20px;
  font-style: italic;
  background: initial;
  font-weight: 700;
  position: relative;
  display: inline-block;
  color: #efd087 !important;

  margin-right: 0px !important;

  // text-shadow: 0 0.01rem 0 rgba(0, 0, 0, 0.4);
  // transform: scale(0.6);
}
.svgIcon {
  width: 7.7vw;
}
.IndexMenu {
  height: 105px;
  width: 100%;
  position: relative;

  align-items: center;
  display: flex !important;
  justify-content: space-between;
  position: relative;
  padding: 10px;

  .loginDiv {
    display: flex;
    flex-direction: column;
    height: 100%;
    justify-content: center;
    padding: 10px 5px;
    width: 290px;
    box-sizing: border-box;
    font-size: 22px;

    .content {
      display: inline-flex;
      width: 100%;
      gap: 20px;

      .login-btn,
      .register-btn {
        width: 130px;
        height: 50px;
        border-radius: 10px;
        padding: 8px 15px;
        font-size: 18px;
        font-weight: normal;
        cursor: pointer;
        border: none;
        outline: none;
      }

      .login-btn {
        background-color: transparent;
        color: var(--theme-primary-color);
        border: 1px solid var(--theme-primary-color);
      }

      .register-btn {
        background-color: var(--theme-primary-color);
        color: var(--theme-main-bg-color);
        border: 1px solid var(--theme-primary-color);
      }
    }

    .name {
      align-items: center;
      display: flex;
      position: absolute;
      left: 20px;
      top: 5px;
      font-size: 3.5vw;

      span {
        color: var(--theme-text-color-darken);
        margin-right: 15px;
      }
    }

    .right-balance {
      display: inline;
      position: absolute;
      left: 10px;
      bottom: 20px;
      min-width: 100px;
      max-width: 300px;
      height: 44px;
      // line-height: 72px;
      font-size: 30px;

      background: var(--theme-top-nav-bg);
      border: 1px solid;
      border-color: var(--theme-color-line);
      border-radius: 85px;

      // div{
      //   white-space: nowrap;
      //   width: 100%;
      //   overflow: hidden;
      // }
      .right-logo {
        // position: absolute;
        // left: 2px;
        // top: 5px;
        // display: inline-block;

        display: inline-block;
        /* 将元素设置为行内块元素 */
        vertical-align: middle;
        /* 垂直居中对齐 */

        width: 33px;
        height: 33px;
        margin-left: 5px;
        // margin-top: 5px;
        // margin-left: 5px
        // margin:  0 auto ;
        // margin-left: 5px;
        // margin-top: -10px;
        // @include webp('/icons/content');
        // background-repeat: no-repeat;
        // background-size: 33px 33px;
      }

      .right-text {
        display: inline-block;
        /* 将元素设置为行内块元素 */
        vertical-align: middle;
        /* 垂直居中对齐 */
        // vertical-align: middle; /* 垂直居中对齐 */
        // width: 100px;
        font-size: 30px;
        // text-align: right;
        // line-height: 44px;
        // word-wrap: break-word; /* 旧版浏览器支持 */
        // overflow-wrap: break-word; /* 标准属性 */
        // padding-top: 6px;
        padding-left: 10px;
        padding-right: 10px;
        color: var(--theme-secondary-color-finance);
        text-decoration: underline; // padding-right: 5px;
      }

      .right-refresh {
        display: inline;
        /* 将元素设置为行内块元素 */
        // vertical-align: middle; /* 垂直居中对齐 */
        margin-top: 5px;
        margin-right: 10px;
        float: right;
        width: 29px;
        height: 31px;
        // @include webp('/img/index/refresh');
        background-image: url(/icons/refresh.webp);
        background-repeat: no-repeat;
        background-size: 29px 31px;

        &.rotate {
          // animation: spin 1s linear infinite;
          -webkit-transform: rotate(360deg);
          transform: rotate(360deg);

          -webkit-transition: -webkit-transform 1s linear;
          transition: transform 1s linear;
        }
      }

      .right-dev {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 74px;
        width: 355px;
        margin-left: 70px;
        // background-color: #D9D9D9;
      }

      .right-add {
        width: 60px;
        margin-right: 75px;
      }
    }
  }

  .menuDiv {
    display: flex;
    flex-wrap: wrap;
    box-sizing: border-box;
    font-size: 14px;

    .menu-btn {
      text-align: center;

      align-items: center;
      display: flex;
      flex: 1;
      flex-direction: column;
      margin-right: 5px;
      max-width: 100px;
      min-width: 90px;
      position: relative;
      box-sizing: border-box;

      span {
        word-wrap: break-word;
        display: block;
        font-size: 20px;
        text-overflow: clip;
        -webkit-box-orient: vertical;
        color: var(--theme-text-color);
        line-height: 1.1;
        margin-top: 6.2px;
        overflow: hidden;
        text-align: center;
        width: 100px;
      }
    }
  }

  .menuPoup {
    top: 100px;
    background-color: var(--theme-main-bg-color);
    border: 1px solid var(--theme-color-line);
    border-radius: 14px;
    // -webkit-box-shadow: 0 .04rem .12rem 0 rgba(0,0,0,.1);
    box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.1);
    overflow: auto;
    position: absolute;
    right: 10px;
    // top: 1.05rem;
    z-index: 99;
    display: block;
    -webkit-font-smoothing: antialiased;
    // -moz-osx-font-smoothing: grayscale;
    // border: 0;
    color: var(--theme-text-color);
    font-size: 14px;
    // margin: 0;
    // overflow: hidden;
    // padding: 0;
    // -webkit-user-select: none;
    // -moz-user-select: none;
    // -ms-user-select: none;
    user-select: none;
    width: 544px;
    height: 420px;
    padding: 20px 10px 10px 10px;

    .menuLayout {
      display: flex;
      flex-wrap: wrap;

      .menuItem {
        align-items: center;
        display: flex;
        flex: 1;
        flex-direction: column;
        margin-right: 5px;
        max-width: 100px;
        min-width: 90px;
        position: relative;
        margin-bottom: 30px;

        span {
          line-height: 1.2;
          word-wrap: break-word;
          display: block;
          font-size: 20px;
          text-overflow: clip;
          color: var(--theme-text-color);
          margin-top: 6.2px;
          overflow: hidden;
          text-align: center;
          width: 100px;
        }
      }
    }
  }

  .overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: transparent;
    // z-index: 1;
  }
}
</style>
