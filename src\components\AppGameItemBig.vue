<script setup lang="ts" name="AppGameItemBig">
import { GameItem, GameSoltTypeEnum, PGSlotsImgTypeEnum } from "~/types/common";
import { GameNavEnum } from "~/types/common";
interface Props {
  data: GameItem;
  itemWidth?: number;
  itemHeight?: number;
  imgWidth?: number;
  imgHeight?: number;
}

const props = withDefaults(defineProps<Props>(), {});
const innerStyle = computed(() => {
  const obj: any = {
    width:
      props.itemWidth && props.itemWidth > 0
        ? `var(--app-px-${props.itemWidth})`
        : `var(--app-px-235)`,
    height:
      props.itemHeight && props.itemHeight > 0
        ? `var(--app-px-${props.itemHeight})`
        : `var(--app-px-280)`,
  };
  return obj;
});
const gameIconInnerStyle = computed(() => {
  const obj: any = {
    width:
      props.imgWidth && props.imgWidth > 0
        ? `var(--app-px-${props.imgWidth})`
        : `var(--app-px-210)`,
    height:
      props.imgHeight && props.imgHeight > 0
        ? `var(--app-px-${props.imgHeight})`
        : `var(--app-px-280)`,
  };
  return obj;
});
const router = useRouter();

const appStore = useAppStore();
const { isLogin, getRemoteImgUrl, isShowStar, isShowGood, PGSlotsImgType } =
  storeToRefs(appStore);

const gameStore = useGameStore();
const { gameNavInit } = storeToRefs(gameStore);

const plat = computed(() => {
  if (gameNavInit.value && gameNavInit.value[props.data.game_type]) {
    return gameNavInit.value[props.data.game_type].filter(
      (p: any) => p.id === props.data.platform_id
    )[0];
  }
  return undefined;
});

// const isLoading = ref(true);

// const imgLoad = () => {
//   isLoading.value = false;
// }

// const { run: runInsertLoveGame } = useRequest(() => ApiInsertLoveGame({ id: props.data.id, ty: props.data.game_type }), {
//   manual: true,
//   onSuccess: () => {
//     beLoved.value = true
//   }
// })

// const { run: runDeleteLoveGame } = useRequest(() => ApiDeleteLoveGame({ id: props.data.id }), {
//   manual: true,
//   onSuccess: () => {
//     beLoved.value = false
//   }
// })

// const isFav = computed(() => +props.data.is_fav === 1 ? true : false)

// const beLoved = ref(+props.data.is_fav === 1 ? true : false)

// const loveGame = () => {
//   if (!isLogin.value) {
//     appStore.setLoginDialogVisible(true)
//   } else {
//     if (beLoved.value) {
//       runDeleteLoveGame()
//     } else {
//       runInsertLoveGame()
//     }
//   }
// }

// const { run: runLunch } = useRequest(() => ApiLunchGame({ pid: props.data.platform_id + '', code: props.data.game_id }), {
//   manual: true,
//   onSuccess: (data) => {
//     if (data && data.indexOf('http') !== -1) {
//       router.push({ path: '/embedded', query: { url: data } })
//       // window.open(data)
//     }
//   }
// })

const { run: runLunch } = useRequest(
  () =>
    ApiLunchGame({
      pid: props.data.platform_id + "",
      code: props.data.game_id,
      game_type: props.data.game_type,
    }),
  {
    manual: true,
    onSuccess: (data) => {
      if (data) {
        window.localStorage.removeItem("callbackHTML");
        window.localStorage.setItem("callbackHTML", data);
        router.push({ path: "/embedded", query: { url: "" } });
        // window.open(data)
      }
    },
    onError(err: any) {
      console.log(err);
      if (err.data.msg && err.data.msg == 5001) {
        showToast(
          "Seu saldo é menor que R$" +
            err.data.data +
            ", Por favor, faça um depósito para jogar"
        );
      }
    },
    onAfter: () => {
      appStore.setIsShowLoading(false);
      gameStore.setGameItemPopupVisible(false);
    },
  }
);

const lunchGame = () => {
  if (isLogin.value) {
    //显示loading
    appStore.setIsShowLoading(true);
    runLunch();
    // gameStore.setGameItemPopupVisible(true, props.data)
  } else {
    appStore.setLoginDialogVisible(true);
  }

  saveGame();
  // router.push({ path: '/embedded', query: { url: '' } })
};

//保存数据
const saveGame = () => {
  let recenteData = window.localStorage.getItem("recenteData");
  if (recenteData == null) {
    let mapSet: any = [];
    mapSet.push(props.data);
    window.localStorage.setItem("recenteData", JSON.stringify(mapSet));
  } else {
    let loadedMap = JSON.parse(recenteData);
    let curPos = -1;
    for (let i = 0; i < loadedMap.length; i++) {
      if (loadedMap[i].id == props.data.id) {
        curPos = i;
        break;
      }
    }

    if (curPos >= 0) {
      const element = loadedMap.splice(curPos, 1)[0];
      loadedMap.unshift(element);
    } else {
      loadedMap.unshift(props.data);
    }

    if (loadedMap.length > 20) {
      loadedMap = loadedMap.splice(0, 20);
    }

    window.localStorage.setItem("recenteData", JSON.stringify(loadedMap));
  }

  // window.localStorage.removeItem('recenteData')
};

const defaultPGSlotsPath =
  "/img/gameItem/big-" + PGSlotsImgTypeEnum[PGSlotsImgType.value] + ".webp";
// 打开页签-游戏列表
const openPGSlotsPage = () => {
  router.push("/subgame?platform_id=" + GameSoltTypeEnum.Slot_pg);
};
</script>

<template>
  <div class="app-game-item" :style="innerStyle">
    <div class="item_content">
      <!-- <div class="item_content_focus">
        <AppImage class="focus_img" :class="{ loved: beLoved }"
          :src="(beLoved) ? '/icons/i-fav.png' : '/icons/i-focus.png'" alt="" @click="loveGame" />
      </div> -->
      <!-- <AppLoading v-if="isLoading" class="game-loading" /> -->
      <!-- <AppSpinner v-if="isLoading" :size="46" :stroke-width="10" color="#1373EF" /> -->
      <!-- <div class="game-img" :style="{backgroundImage: 'url(https://www.cloudstoragehubs.com/resource'+data.img+')'}" @click="lunchGame"></div> -->

      <div class="game-img game-default-img" :style="gameIconInnerStyle"></div>
      <div class="game-text" v-if="data.platform_id == '603'">{{data.en_name}}</div>
      <div
        v-if="data.isPGSlots"
        class="game-img"
        v-lazy:background-image="defaultPGSlotsPath"
        @click="openPGSlotsPage"
        :style="gameIconInnerStyle"
      ></div>
      <div
        v-else
        class="game-img"
        v-lazy:background-image="
          isFullPath(data.img || '') ? data.img : getRemoteImgUrl + data.img
        "
        @click="lunchGame"
        :style="gameIconInnerStyle"
      ></div>
      <AppImage
        v-if="isShowGood && data.isHot"
        class="good_img"
        src="/img/gameItem/good.webp"
        alt=""
      />
      <AppImage
        v-if="isShowStar && data.isHot"
        class="star_img"
        src="/img/gameItem/star.webp"
        alt=""
      />

      <!-- <label class="name"> {{ data1.br_alias }}</label> -->
      <!-- <label class="name"> 12314111111146545611111111111111111114</label> -->
      <!-- <AppImage class="game-img" v-lazy="'https://www.cloudstoragehubs.com/resource+data.img" @click="lunchGame" /> -->
    </div>
    <!-- <div class="title">
      <div class="text">
        {{ data.br_alias || (plat ? plat.name : '') }}
      </div>
    </div> -->
  </div>
</template>
<style>
:root {
  --app-game-item-heightP: 280px;
  --app-game-item-widthP: 235px;

  --app-game-item-height: 280px;
  --app-game-item-width: 210px;
}
</style>
<style lang="scss" scoped>
// @import '../../theme/mixin.scss';

.app-game-item {
  width: 33.3%;
  height: 280px;
  scroll-snap-align: start;
  // background: rgba(32, 53, 91, 0.5);
  // background: rgba(14, 48, 117, 0.5);
  // background: #122039;
  border-radius: 10px;
  position: relative;
  margin: 15px 0;

  .game-img {
    width: 210px;
    height: 280px;
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    transition: opacity 0.5s;
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    opacity: 0;
    object-fit: cover;
    border-radius: 10px;
    overflow: hidden;
  }
  .game-default-img {
    background-image: url("/img/index/bigDefault.webp");
    opacity: 1;
  }

  .game-text {
    position: absolute;
    z-index: 1;
    bottom: 1.5vw;
    font-size: 18px;
    color: white;
    width: var(--app-px-150);
    text-align: center;
  }

  .star_img {
    position: absolute;
    width: 42px;
    top: 6px;
    right: 18px;
    border-radius: 50%;
    box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.3);
  }
  .good_img {
    position: absolute;
    width: 41px;
    top: 0px;
    left: 11px;
  }
  .name {
    position: absolute;
    display: inline-block;
    width: 160px;
    height: 48px;
    text-align: center;
    color: var(--theme-text-color);
    font-size: 22px;
    top: 175px;
    line-height: 24px;
    word-wrap: break-word;
    text-overflow: ellipsis;

    // letter-spacing:20px;
  }

  .game-img[lazy="loading"] {
    opacity: 0;
  }

  .game-img[lazy="error"] {
  }

  .game-img[lazy="loaded"] {
    opacity: 1;
  }

  img.game-img[lazy="loading"] {
    opacity: 0;
  }

  img.game-img[lazy="error"] {
  }

  img.game-img[lazy="loaded"] {
    opacity: 1;
  }

  .item_content {
    width: 100%;
    height: 100%;
    position: relative;
    border-radius: 20px;
    overflow: hidden;

    display: flex;
    align-items: center;
    justify-content: center;
    // background-color: #fff;
    .item_content_focus {
      width: 35px;
      height: 35px;
      // background: rgba(0, 0, 0, 0.5);
      position: absolute;
      top: 0;
      right: 0;
      border-radius: 0 10px 0 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1;

      .focus_img {
        width: 18px;
      }
    }
  }

  .title {
    font-size: 28px;
    width: 100%;
    height: 94px;
    background-image: url("/img/gameItemNameBg.webp");
    background-position: center center;
    background-size: 100% 100%;
    font-weight: 500;
    overflow: hidden;
    word-break: break-all;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    bottom: 0;
    color: #fff;
    .text {
      max-height: 56px;
      overflow: hidden;
      display: flex;
      align-items: flex-start;
      justify-content: flex-start;
    }
  }

  // .title {
  //   font-size: 28px;
  //   width: 100%;
  //   height: 80px;
  //   background-color: var(--app-game-title-bg-color);
  //   color: #fff;
  //   font-weight: 700;
  //   // line-height: 28px;
  //   // padding-top: 7px;
  //   // padding-bottom: 7px;
  //   overflow: hidden;
  //   // padding-left: 12px;
  //   // padding-right: 12px;
  //   word-break: break-all;
  //   text-align: center;
  //   display: flex;
  //   align-items: center;
  //   justify-content: center;
  //   position: absolute;
  //   border-bottom-left-radius: 20px;
  //   border-bottom-right-radius: 20px;
  //   bottom: 0;
  //   .text {
  //     max-height: 56px;
  //     overflow: hidden;
  //     display: flex;
  //     align-items: flex-start;
  //     justify-content: flex-start;
  //   }
  // }
}
</style>
