<script setup lang="ts" name="app-index-title">
import { log } from "console";
import { GameNavEnum, GameSoltTypeEnum, GameHallTopEnum } from "~/types/common";
const emit = defineEmits(["appGamePlatform-by", "gotoLeft-by", "gotoRight-by"]);
interface Props {
  id: GameNavEnum;
  isCallback: boolean;
  platform_id?: string;
}
const props = withDefaults(defineProps<Props>(), {});

const router = useRouter();

const gameStore = useGameStore();
const { gameNavData } = storeToRefs(gameStore);

const gameType = computed(() => {
  if (props.platform_id) {
    if (Number(props.platform_id) == GameSoltTypeEnum.Slot_pg) {
      return "PG";
    } else if (Number(props.platform_id) == GameSoltTypeEnum.Slot_pp) {
      return "PP";
    } else if (Number(props.platform_id) == GameSoltTypeEnum.Slot_jdb) {
      return "JDB";
    } else if (Number(props.platform_id) == GameSoltTypeEnum.Slot_fc) {
      return "FC";
    } else if (Number(props.platform_id) == GameSoltTypeEnum.Slot_haba) {
      return "HABA";
    } else if (Number(props.platform_id) == GameSoltTypeEnum.Slot_tada) {
      return "TADA";
    } else if (Number(props.platform_id) == GameSoltTypeEnum.Slot_yesbingo) {
      return "YesBingo";
    } else if (Number(props.platform_id) == GameHallTopEnum.Recente) {
      return "Recete";
    } else if (Number(props.platform_id) == GameHallTopEnum.Favoritos) {
      return "Favoritos";
    }
  } else {
    if (gameNavData.value) {
      return gameNavData.value.filter((i) => i.id === props.id)[0]
        ? gameNavData.value.filter((i) => i.id === props.id)[0].name
        : "";
    }
  }

  return undefined;
});

const gameTypeIcon = computed(() => {
  if (props.platform_id) {
    return "/icons/nav_" + props.platform_id + ".png";
  } else {
    return "/icons/nav_" + props.id + ".png";
  }
});

const goPage = () => {
  console.log(props.id);
  
  if (props.isCallback) {
    emit("appGamePlatform-by");
    return;
  }

  if(props.id === GameNavEnum.Blockchain){
    router.push("/subgame/Blockchain");
    return;
  }

  if(props.id === GameNavEnum.AoVivo){
    router.push("/subgame/AoVivo");
    return;
  }

  if(props.id === GameNavEnum.Pescaria){
    router.push("/subgame/Pescaria");
    return;
  }

  if (props.id === GameNavEnum.Quente) {
    return;
  }
  if (props.id === GameNavEnum.Dentro_De_Casa) {
    router.push("/rec-fav-game");
    return;
  }
  if (props.id === GameNavEnum.Slots) {
    router.push("/subgame");
    return;
  }
  router.push("/game-list/" + props.id);
};

const gotoLeft = () => {
  emit("gotoLeft-by");
};

const gotoRight = () => {
  emit("gotoRight-by");
};

const isShowTudo = ref(true);
if (props.platform_id) {
  if (
    Number(props.platform_id) == GameHallTopEnum.Recente ||
    Number(props.platform_id) == GameHallTopEnum.Favoritos
  ) {
    isShowTudo.value = false;
  }
}

// const hahaIcons = [
//   { id: GameNavEnum.Quente, icon: '', },
//   { id: GameNavEnum.Dentro_De_Casa, icon: '' },
//   { id: GameNavEnum.Slot, icon: '' },
//   { id: GameNavEnum.Pesca, icon: '' },
//   { id: GameNavEnum.Pôquer, icon: '' },
//   { id: GameNavEnum.Esporte, icon: '' },
//   { id: GameNavEnum.Ao_Vivo, icon: '' },
// ]
</script>

<template>
  <div class="app-index-title">
    <!-- <AppImage class="t-img" :src="`/img/index-title${id}.png`" alt="" /> -->
    <!-- <div class="title-1">Recomendações</div> -->
    <!-- <AppImage class="title-img" :src="`/icons/g_title_${id}.png`" /> -->
    <!-- <span>Recomendações </span> -->
    <div class="left">
      <AppImage class="icon_0" :src="gameTypeIcon" alt="" />
      <!--:class="`icon_${id}`"-->
      <span v-if="gameType">{{ gameType }}</span>
      <span v-else-if="gameTypeIcon == '/icons/nav_2501.png'">Blockchain</span>
      <span v-else-if="gameTypeIcon == '/icons/nav_2502.png'">Ao Vivo</span>
      <span v-else-if="gameTypeIcon == '/icons/nav_2503.png'">Pescaria</span>

      <span v-else>Slots{{ gameType }}</span>
    </div>
    <div class="right">
      <!---&& id !== GameNavEnum.Slots  v-if="id !== GameNavEnum.Quente "--->

      <div class="todos">
        <!-- <AppImage class="img1"  @click="gotoLeft"  src="/icons/hall_showMore.png" ></AppImage>  -->
        <label v-if="isShowTudo" class="tudos" @click="goPage">Tudo</label>
        <!-- <AppImage class="img2"  @click="gotoRight" src="/icons/hall_showMore.png" alt="" /> -->
      </div>
      <!-- <AppImage src="/icons/btn_arrows.png" class="img1"></AppImage> -->
      <!-- <AppImage src="/icons/hall_showMore.png" class="img2"></AppImage> -->
      <!-- <span @click="goPage">More</span> -->
    </div>
  </div>
</template>

<style lang="scss" scoped>
.todos {
  font-size: 24px;
  // background-color: #0E3D8F;
  border-radius: 10px;
  width: 200px;
  height: 46px;
  color: #ffffff;
  // text-align: center;
}

.title-img {
  width: 55px;
}
.app-index-title {
  width: 100%;
  color: #172554;
  font-size: var(--app-gameTitle-fontSize);
  // font-weight: 700;
  line-height: 40px;
  padding-bottom: 10px;
  padding-top: 0px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  // border-bottom: 1px solid #28374d;
  margin-bottom: 0px;
  .left {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 14px;
    color: var(--app-title-color);
    .icon_0 {
      width: 64px; // 统一大小
    }
    // .icon_100{
    //   width: 34px;
    // }
    // .icon_6{
    //   width: 50px;
    // }
    // .icon_600{
    //   width: 50px;
    // }
    // .icon_2{
    //   width: 56px;
    // }
    // .icon_1{
    //   width: 40px;
    // }
    // .icon_7{
    //   width: 44px;
    // }
    // .icon_8{
    //   width: 36px;
    // }
    // .icon_1000{
    //   width: 54px;
    // }
  }
  .right {
    color: rgba(255, 255, 255, 0.6);
    font-size: 24px;
    font-weight: 500;
    line-height: 46px;
    position: relative;

    .tudos {
      // background-color: #172554;
      color: var(--theme-text-color-lighten);
      display: block;
      width: 80px;
      text-align: center;
      margin-left: 120px;
    }
    button {
      display: inline-block;
      // margin-right: 120px;
    }
    img {
      top: 4px;
      position: absolute;
      width: 38px;
      height: 38px;
    }
    .img1 {
      transform: rotate(180deg);
      width: 16px;
      height: 27px;
      margin-top: 5px;
      left: 20px;
    }
    .img2 {
      // transform: rotate(180deg);
      width: 16px;
      height: 27px;
      margin-top: 5px;
      right: 20px;
    }
  }

  .title-1 {
    margin-bottom: 5px;
  }

  .t-img {
    width: 100%;
    vertical-align: middle;
  }
}
</style>
