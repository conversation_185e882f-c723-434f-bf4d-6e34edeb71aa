<script setup lang="ts" name="seguranca">
const router = useRouter();
const appStore = useAppStore();
const { userInfo } = storeToRefs(appStore);
//隐藏底下菜单
appStore.setFooterDialogVisble(false);
const userInfoData = ref([
  {
    icon: "/img/user/Nome-de-Usuario",
    label: "Nome de Usuário",
    data: userInfo.value.username,
    width: 30,
    svg: `<svg
            t="1745430388137"
            class="icon"
            viewBox="0 0 1024 1024"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            p-id="42155"
            width="19"
            height="19"
          >
            <path
              d="M277.333333 320a234.666667 234.666667 0 1 1 469.333334 0 234.666667 234.666667 0 0 1-469.333334 0zM512 170.666667a149.333333 149.333333 0 1 0 0 298.666666 149.333333 149.333333 0 0 0 0-298.666666zM397.909333 618.666667H626.090667c52.352 0 88.106667 0 118.485333 9.173333a213.333333 213.333333 0 0 1 142.250667 142.250667c9.216 30.378667 9.173333 66.133333 9.173333 118.485333V896a42.666667 42.666667 0 0 1-85.333333 0c0-62.634667-0.426667-84.48-5.546667-101.12a128 128 0 0 0-85.333333-85.333333c-16.64-5.12-38.485333-5.546667-101.12-5.546667h-213.333334c-62.634667 0-84.48 0.426667-101.12 5.546667a128 128 0 0 0-85.333333 85.333333c-5.12 16.64-5.546667 38.485333-5.546667 101.12a42.666667 42.666667 0 1 1-85.333333 0v-7.424c0-52.352 0-88.106667 9.173333-118.485333a213.333333 213.333333 0 0 1 142.250667-142.250667c30.378667-9.216 66.133333-9.216 118.485333-9.173333z"
              fill="var(--svg-icon-color)"
              p-id="42156"
            ></path>
          </svg>`,
  },
  {
    icon: "/img/user/No-de-Telefone",
    label: "No de Telefone",
    data: userInfo.value.phone,
    width: 25,
    svg: `<svg
            t="1745430002393"
            class="icon"
            viewBox="0 0 1024 1024"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            p-id="28165"
            width="19"
            height="19"
          >
            <path
              d="M217.6 80.128a128 128 0 0 1 115.541333 0c18.432 9.301333 34.474667 25.386667 52.992 43.946667l4.096 4.096 8.32 8.32 2.688 2.688c12.245333 12.245333 22.784 22.741333 30.165334 34.133333a128 128 0 0 1 0 139.52c-7.381333 11.349333-17.92 21.888-30.165334 34.133333l-2.688 2.645334a269.056 269.056 0 0 0-4.608 4.650666 3.84 3.84 0 0 0-0.042666 0.469334 91.562667 91.562667 0 0 0 2.133333 4.565333 580.437333 580.437333 0 0 0 113.152 159.36 580.352 580.352 0 0 0 163.882667 115.285333h0.256l0.256-0.085333 4.650666-4.565333 2.688-2.688c12.202667-12.245333 22.698667-22.784 34.090667-30.165334a128 128 0 0 1 139.52 0c11.392 7.381333 21.888 17.92 34.133333 30.165334l2.688 2.688 8.277334 8.32 4.096 4.053333c18.602667 18.56 34.645333 34.56 43.989333 53.034667a128 128 0 0 1 0 115.541333c-9.344 18.474667-25.386667 34.474667-43.946667 53.034667l-4.138666 4.053333-6.698667 6.741333-2.901333 2.901334c-19.84 19.882667-35.072 35.114667-55.637334 46.677333-23.466667 13.141333-57.173333 21.930667-84.096 21.845333-23.552-0.085333-40.704-4.949333-62.848-11.221333l-2.432-0.682667a854.954667 854.954667 0 0 1-371.925333-218.837333 854.997333 854.997333 0 0 1-219.52-374.357333c-6.272-22.144-11.178667-39.296-11.221333-62.848a186.24 186.24 0 0 1 21.845333-84.096c11.562667-20.565333 26.794667-35.797333 46.677333-55.637334l2.901334-2.901333 6.698666-6.741333 4.096-4.053334c18.517333-18.602667 34.56-34.688 53.034667-43.989333z m77.013333 76.117333a42.666667 42.666667 0 0 0-38.528 0c-4.522667 2.304-10.24 7.253333-35.242666 32.298667l-6.741334 6.698667c-23.893333 23.893333-30.336 30.805333-35.498666 39.978666-5.973333 10.666667-10.965333 29.866667-10.922667 42.069334 0 10.837333 1.621333 17.365333 8.661333 42.24a769.664 769.664 0 0 0 197.077334 334.933333 769.664 769.664 0 0 0 334.933333 197.034667c24.832 7.04 31.36 8.618667 42.197333 8.661333 12.202667 0.042667 31.402667-4.949333 42.026667-10.922667 9.173333-5.12 16.085333-11.605333 40.021333-35.498666l6.698667-6.741334c25.045333-25.045333 29.994667-30.72 32.256-35.285333a42.666667 42.666667 0 0 0 0-38.485333c-2.261333-4.565333-7.210667-10.24-32.256-35.285334l-8.32-8.32c-16.426667-16.469333-20.138667-19.84-22.954667-21.632a42.666667 42.666667 0 0 0-46.506666 0c-2.816 1.792-6.485333 5.162667-22.954667 21.632l-1.024 1.024c-3.84 3.84-8.661333 8.704-14.506667 12.842667a87.253333 87.253333 0 0 1-73.514666 12.544 106.24 106.24 0 0 1-17.92-7.296 665.6 665.6 0 0 1-182.784-129.706667 665.770667 665.770667 0 0 1-129.706667-182.826666l-0.512-1.024c-2.133333-4.352-4.821333-9.984-6.826667-16.853334a87.253333 87.253333 0 0 1 12.544-73.557333c4.181333-5.802667 9.045333-10.666667 12.885334-14.506667l1.024-0.981333c16.426667-16.469333 19.797333-20.138667 21.632-22.954667a42.666667 42.666667 0 0 0 0-46.506666c-1.834667-2.816-5.205333-6.528-21.632-22.997334l-8.32-8.277333c-25.045333-25.045333-30.72-29.994667-35.285334-32.298667z"
              p-id="28166"
              fill="var(--svg-icon-color)"
            ></path>
          </svg>`,
  },
  {
    icon: "/img/user/Endereco-de-e-mail",
    label: "Endereço de e-mail",
    data: userInfo.value.email,
    width: 30,
    svg: `<svg
            t="1745430212995"
            class="icon"
            viewBox="0 0 1024 1024"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            p-id="28445"
            width="19"
            height="19"
          >
            <path
              d="M288.384 128H735.573333c34.346667 0 62.72 0 85.76 1.877333 24.021333 1.962667 46.08 6.186667 66.773334 16.725334a170.666667 170.666667 0 0 1 74.581333 74.581333c10.197333 19.968 14.464 41.216 16.512 64.213333a42.666667 42.666667 0 0 1 1.28 21.333334c0.853333 19.2 0.853333 41.429333 0.853333 66.986666v276.565334c0 34.346667 0 62.72-1.92 85.76-1.962667 24.021333-6.186667 46.08-16.725333 66.773333a170.666667 170.666667 0 0 1-74.581333 74.581333c-20.693333 10.538667-42.752 14.762667-66.730667 16.725334-23.082667 1.877333-51.456 1.877333-85.76 1.877333H288.341333c-34.346667 0-62.72 0-85.76-1.877333-24.021333-1.962667-46.08-6.186667-66.773333-16.725334a170.666667 170.666667 0 0 1-74.581333-74.581333c-10.538667-20.693333-14.762667-42.752-16.725334-66.730667C42.666667 712.96 42.666667 684.629333 42.666667 650.325333V373.674667c0-25.557333 0-47.786667 0.768-66.986667a42.666667 42.666667 0 0 1 1.322666-21.333333c2.048-23.04 6.314667-44.245333 16.512-64.213334a170.666667 170.666667 0 0 1 74.581334-74.581333c20.693333-10.538667 42.752-14.762667 66.730666-16.725333C225.706667 128 254.037333 128 288.341333 128zM128 380.586667v267.946666c0 16.341333 0 30.378667 0.170667 42.581334l193.578666-174.890667-193.706666-135.594667zM413.866667 476.544l1.450666 1.024 42.88 30.037333c31.146667 21.76 37.717333 25.557333 43.52 27.008a42.666667 42.666667 0 0 0 20.650667 0c5.802667-1.450667 12.373333-5.248 43.52-27.008l42.88-30.037333a41.130667 41.130667 0 0 1 1.493333-1.024l282.24-197.546667a65.109333 65.109333 0 0 0-5.76-19.029333 85.333333 85.333333 0 0 0-37.290666-37.290667c-6.656-3.413333-16.213333-6.186667-34.944-7.68C795.264 213.333333 770.432 213.333333 733.866667 213.333333H290.133333c-36.522667 0-61.397333 0-80.597333 1.621334-18.688 1.493333-28.245333 4.266667-34.944 7.68a85.333333 85.333333 0 0 0-37.290667 37.290666c-2.261333 4.437333-4.266667 10.154667-5.76 18.986667l282.282667 197.589333z m-20.352 89.941333l-241.493334 218.154667c6.485333 6.784 14.122667 12.458667 22.613334 16.768 6.698667 3.413333 16.213333 6.186667 34.986666 7.68C228.736 810.666667 253.568 810.666667 290.133333 810.666667h443.733334c36.565333 0 61.397333 0 80.597333-1.621334 18.730667-1.493333 28.288-4.266667 34.986667-7.68 8.448-4.309333 16.085333-9.984 22.613333-16.768l-241.493333-218.154666-15.786667 11.050666a1803.093333 1803.093333 0 0 0-4.949333 3.413334c-23.210667 16.341333-43.648 30.677333-66.858667 36.48a128 128 0 0 1-61.952 0c-23.210667-5.802667-43.605333-20.138667-66.858667-36.437334a1805.909333 1805.909333 0 0 0-4.906666-3.456l-15.786667-11.050666z m308.778666-50.218666l193.621334 174.890666C896 678.826667 896 664.874667 896 648.533333V380.586667l-193.706667 135.68z"
              p-id="28446"
              fill="var(--svg-icon-color)"
            ></path>
          </svg>`,
  },
]);

const passwordData = ref([
  {
    icon: "/img/user/Senha-de-Login",
    label: "Senha de Login",
    data: userInfo.value.username,
    svg: `<svg t="1745431288538" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="42365" width="19" height="19"><path d="M597.333333 298.666667a42.666667 42.666667 0 0 1 42.666667-42.666667c32.682667 0 65.536 12.501333 90.496 37.504 25.002667 24.96 37.504 57.813333 37.504 90.453333A42.666667 42.666667 0 1 1 682.666667 384a42.410667 42.410667 0 0 0-12.501334-30.165333A42.410667 42.410667 0 0 0 640 341.333333a42.666667 42.666667 0 0 1-42.666667-42.666666z" p-id="42366" fill="#1afa29"></path><path d="M341.333333 384a298.666667 298.666667 0 1 1 258.56 295.978667 611.498667 611.498667 0 0 0-21.930666-2.688l-0.426667 0.426666a381.525333 381.525333 0 0 0-10.666667 10.410667l-67.370666 67.413333A42.666667 42.666667 0 0 1 469.333333 768h-42.666666v42.666667a42.666667 42.666667 0 0 1-42.666667 42.666666H341.333333v42.666667a42.666667 42.666667 0 0 1-42.666666 42.666667H194.901333c-10.752 0-21.248 0-30.122666-0.725334a86.272 86.272 0 0 1-32.853334-8.576 85.333333 85.333333 0 0 1-37.290666-37.290666 86.272 86.272 0 0 1-8.576-32.853334C85.333333 850.346667 85.333333 839.850667 85.333333 829.098667v-75.52-2.133334c0-8.106667-0.042667-18.346667 2.346667-28.373333a85.333333 85.333333 0 0 1 10.24-24.661333c5.376-8.789333 12.629333-16 18.346667-21.717334l1.536-1.536 218.069333-218.026666c5.376-5.376 8.362667-8.405333 10.453333-10.666667l0.341334-0.426667-0.128-1.92c-0.426667-4.394667-1.28-10.410667-2.56-20.010666A301.056 301.056 0 0 1 341.333333 384z m0 384v-42.666667a42.666667 42.666667 0 0 1 42.666667-42.666666h67.669333l54.869334-54.869334 2.218666-2.261333c7.68-7.68 16.981333-17.066667 27.562667-22.997333a78.506667 78.506667 0 0 1 29.568-9.984c8.234667-1.152 16.298667-0.597333 22.186667 0 6.186667 0.597333 13.824 1.621333 22.4 2.773333l0.768 0.085333a213.333333 213.333333 0 1 0-182.656-182.656l0.085333 0.768c1.152 8.576 2.176 16.213333 2.773333 22.357334 0.597333 5.973333 1.152 13.994667 0 22.229333-1.621333 11.306667-4.437333 19.626667-9.984 29.568-5.973333 10.581333-15.317333 19.882667-23.04 27.562667l-2.218666 2.218666-218.026667 218.026667a264.106667 264.106667 0 0 0-7.296 7.466667l-0.085333 0.085333v0.170667a263.68 263.68 0 0 0-0.128 10.410666V827.733333a349.312 349.312 0 0 0 0.469333 25.088l0.597333 0.085334c4.949333 0.426667 11.861333 0.426667 24.533334 0.426666H256v-42.666666a42.666667 42.666667 0 0 1 42.666667-42.666667h42.666666z" p-id="42367" fill="var(--svg-icon-color)"></path></svg>`,
  },
  {
    icon: "/img/user/Senha-de-Saque",
    label: "Senha de Saque",
    data: userInfo.value.pay_password,
    svg: `<svg t="1745431385280" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="42756" width="19" height="19"><path d="M213.333333 597.333333a42.666667 42.666667 0 0 1 42.666667-42.666666h213.333333a42.666667 42.666667 0 1 1 0 85.333333H256a42.666667 42.666667 0 0 1-42.666667-42.666667z" p-id="42757" fill="#13227a"></path><path d="M220.202667 170.666667H803.84c22.485333 0 41.898667 0 57.856 1.28 16.853333 1.408 33.664 4.437333 49.792 12.672a128 128 0 0 1 55.936 55.936c8.234667 16.128 11.264 32.938667 12.629333 49.792 1.322667 16 1.322667 35.413333 1.322667 57.856v327.594666c0 22.485333 0 41.856-1.28 57.856-1.408 16.853333-4.437333 33.664-12.672 49.792a128 128 0 0 1-55.936 55.936c-16.128 8.234667-32.938667 11.264-49.792 12.629334-16 1.322667-35.413333 1.322667-57.856 1.322666H220.16c-22.485333 0-41.856 0-57.856-1.28-16.853333-1.408-33.664-4.437333-49.792-12.672a128 128 0 0 1-55.936-55.936c-8.234667-16.128-11.264-32.938667-12.629333-49.792C42.666667 717.653333 42.666667 698.24 42.666667 675.754667v-249.045334-78.506666c0-22.485333 0-41.856 1.28-57.856 1.408-16.853333 4.437333-33.664 12.672-49.792a128 128 0 0 1 55.936-55.936c16.128-8.234667 32.938667-11.264 49.792-12.629334C178.346667 170.666667 197.76 170.666667 220.202667 170.666667zM128 469.333333v204.8c0 24.618667 0 40.490667 1.024 52.565334 0.938667 11.605333 2.56 15.872 3.626667 18.005333a42.666667 42.666667 0 0 0 18.645333 18.645333c2.133333 1.066667 6.4 2.688 18.005333 3.626667C181.333333 768 197.248 768 221.866667 768h580.266666c24.618667 0 40.490667 0 52.565334-1.024 11.605333-0.938667 15.872-2.56 18.005333-3.626667a42.666667 42.666667 0 0 0 18.645333-18.645333c1.066667-2.133333 2.688-6.4 3.626667-18.005333C896 714.666667 896 698.752 896 674.133333V469.333333H128z m768-85.333333v-34.133333c0-24.618667 0-40.490667-1.024-52.565334-0.938667-11.605333-2.56-15.914667-3.626667-18.005333a42.666667 42.666667 0 0 0-18.645333-18.645333c-2.133333-1.066667-6.4-2.688-18.005333-3.626667C842.666667 256 826.752 256 802.133333 256H221.866667c-24.618667 0-40.490667 0-52.565334 1.024-11.605333 0.938667-15.914667 2.56-18.005333 3.626667a42.666667 42.666667 0 0 0-18.645333 18.645333c-1.066667 2.133333-2.688 6.4-3.626667 18.005333C128 309.333333 128 325.248 128 349.866667V384h768z" p-id="42758" fill="var(--svg-icon-color)"></path></svg>`
  },
]);

const copyName = () => {
  copy(userInfo.value.uid || "");
  showToast("Copied!");
};

const onClickItem_pw = (data, index) => {
  //showToast(index);
  if (index == 0) {
    router.push("/personalCenter/changePassword");
  } else if (index == 1) {
    router.push("/personalCenter/changePayPassword");
  }
};

const onClickItem_uo = (data, index) => {
  //showToast(index);
  if (index == 2) {
    router.push("/personalCenter/changeEmail");
  }
};
//返回
function clickLeft() {
  appStore.setFooterDialogVisble(true);
}
</script>

<template>
  <AppPageTitle
    left-arrow
    title="Segurança"
    title-weight="700"
    @click="clickLeft()"
  />
  <div class="content">
    <div class="content-info">
      <div
        class="content-info-item"
        v-for="(data, index) in userInfoData"
        :key="index"
        @click="onClickItem_uo(data, index)"
      >
        <div class="content-info-item-icon">
          <!-- <AppImage :src="data.icon" :width="data.width" /> -->
          <div v-html="data.svg"></div>
        </div>
        <div class="content-info-item-label">
          {{ data.label }}
        </div>

        <div class="content-info-item-rightEmail">
          {{ data.data != "" ? data.data : "Desvincular" }}
        </div>

        <div
          v-if="index == 2"
          class="content-info-item-right"
          style="scale: 0.6"
        >
          <AppImage
            class="arrow"
            src="/img/user/icon-arrow-green"
            style="position: relative; top: -10px"
          />
        </div>
      </div>
    </div>

    <div class="content-info">
      <div
        class="content-info-item"
        v-for="(data, index) in passwordData"
        :key="index"
        @click="onClickItem_pw(data, index)"
      >
        <div class="content-info-item-icon">
          <!-- <AppImage :src="data.icon" width="30" /> -->
          <div v-html="data.svg"></div>
        </div>
        <div class="content-info-item-label">
          {{ data.label }}
        </div>

        <div v-if="index == 1" class="content-info-item-rightEmail">
          {{ data.data != 0 ? "" : "náo configurado" }}
        </div>

        <div class="content-info-item-right" style="scale: 0.6">
          <AppImage
            class="arrow"
            src="/img/user/icon-arrow-green"
            style="position: relative; top: -10px"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@import "../../theme/mixin.scss";

.content {
  height: calc(100vh - 100px);
  font-size: 30px;
  background-color: var(--theme-bg-color);
  &-info {
    position: relative;
    height: 250px;
    top: 30px;
    margin: 0 30px;
    margin-bottom: 30px;
    border-radius: 15px;
    background-color: var(--theme-sub-bg-color);
    &-item {
      height: 80px;
      padding: 25px 20px;
      width: 100%;
      display: flex;
      &-label {
        position: relative;
        left: 20px;
        color: var(--theme-text-color);
        font-weight: 400;
        font-size: 20px;
        top: 3px;
      }
      &-right {
        position: absolute;
        color: var(--theme-text-color-lighten);
        right: 10px;
      }
      &-rightEmail {
        position: absolute;
        color: var(--theme-text-color-lighten);
        right: 40px;
        padding-top: 3px;
        font-size: 20px;
      }
    }

    &:nth-child(2) {
      height: 170px;
    }
  }
}
</style>
<route lang="yaml">
meta:
  auth: true
</route>
