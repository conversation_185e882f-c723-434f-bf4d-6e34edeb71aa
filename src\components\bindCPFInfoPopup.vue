<script setup lang="ts" name="bindCPFInfo">

const router = useRouter()
const appStore = useAppStore();
const {constCPFInfo, bindCPFInfoVisible,userInfo } = storeToRefs(appStore);

const inputRealNameRef = ref()
const isEdit = computed(() => constCPFInfo.value.length > 0)
watch(isEdit,()=>{

    console.log("constCPFInfo  constCPFInfo    " + isEdit.value);
});
const emit = defineEmits(['updateMessage']);
const updateParent = () => {
    emit('updateMessage',true)
}
// const { userNameReg } = useRegExpUserName();
console.log("bindCPFInfo    " + constCPFInfo.value);

const fromData = reactive({
    real_cpf:'',
    show_cpf:'',
    real_name:'',
})

// showToast("111")
// watchEffect(() => {
//     if (userInfo.value.phone) {
//         fromData.phone = userInfo.value.phone
//     }
// })

// const { run: runResetPwd, loading: resetLoading } = useRequest(() => ApiResetPwd(fromData), {
//   manual: true,
//   onSuccess: () => {
//     appStore.setFindPasswordDialogVisible(false)
//     // localStorage.clear()
//     location.replace(location.origin)
//   }
// })

//关闭界面
const closePage=()=>{
  appStore.setBindCPFInfoVisible(false)
}

const { run: runGetMemberVerify } = useRequest(() => ApiGetMemberVerify(), {
    manual: true,
    onError: (data) => {
        console.log(data)
    },
    onSuccess: (data) => {
      console.log(data)
      if (data){
        if(constCPFInfo.value){
            fromData.show_cpf = constCPFInfo.value;
        } else {
            fromData.show_cpf = data.cpf;
        }
        fromData.real_name = data.real_name;
        onCPFInputChange({data:fromData.show_cpf});
      }
      
      
    }
})

const { run: runUpdateVerifyInfo } = useRequest(() => ApiUpdateVerifyInfo({real_name:"",cpf:fromData.real_cpf}), {
    manual: true,
    onError: (data) => {
        console.log(data)
    },
    onSuccess: (data) => {
      console.log(data)
      if (data == "1000"){
        updateParent();
      }
      
    }
})




const onInputChange = (data:any) =>{
    
}


const onCPFInputChange = (data:any) =>{
    
    if(fromData.show_cpf!="" && data.data){
        const result = fromData.show_cpf.replace(/[.\-]/g, '')
        const a = result.replace(/(\w{3})|(\d{3})/g, '$1.')
        if(a.length>11){
            const s = a.slice(0, 11)
            const s2 = a.slice(12,14)
            fromData.show_cpf = s + "-" + s2;
        }
        else{
            fromData.show_cpf = a;
        } 
    }

}

const themeVars = reactive({
    popoverActionWidth: '660px',
    ActionWidth: '660px',
    width:'660px',
});

watch(bindCPFInfoVisible,()=>{
    if(bindCPFInfoVisible.value){
        fromData.real_name = "";
        fromData.show_cpf = "";
        if(constCPFInfo.value){
            fromData.show_cpf = constCPFInfo.value;
        }
        runGetMemberVerify();
        console.log("runGetMemberVerify");
    }

});

const onClickCPF = () => {
    
    fromData.real_cpf = fromData.show_cpf.replace(/[.\-]/g, '')
    if(fromData.real_cpf.length != 11)return;
    appStore.setIsShowLoading(true)
    runUpdateVerifyInfo()
}



</script>

<template>
     <van-config-provider :theme-vars="themeVars">
        <van-popup class="tip-poup" v-model:show="bindCPFInfoVisible" round>
            <div  class="content" >
                <div :style="{height:`var(--app-px-10)`}"></div>
                <label>Número CPF vinculativo</label>
                <div :style="{height:`var(--app-px-80)`}"></div>
                <AppInput v-if="false" class ="idIput" icon-with="25" v-model="fromData.real_name" :pattern="pixExpReg" ref="inputRealNameRef"
                    icon-left="user-card-id.webp" type="text"
                    placeholder="Introduza o número de 11 dígitos do CPF"
                    msg="Deve ser números puros de 11 bits" :err-height="42" clearable width="662" height="70" maxlength="11" @input="onInputChange"
                    :style-obj="{
                        background: 'transparent',
                        color: 'var(--theme-text-color-darken)',
                        borderRadius: 'var(--app-px-10)',

                    }">
                </AppInput>
                <div v-if="false" :style="{height:`var(--app-px-50)`}"></div>

                <AppInput :disabled="isEdit" class ="idIput" icon-with="25" v-model="fromData.show_cpf" :pattern="pixExpReg" ref="inputRealNameRef"
                    icon-left="user-card-id.webp" type="text"
                    placeholder="Introduza o número de 11 dígitos do CPF"
                    msg="Deve ser números puros de 11 bits" :err-height="42" :clearable="!isEdit" width="662" height="70" maxlength="11" @input="onCPFInputChange"
                    :style-obj="{
                        background: 'transparent',
                        color: 'var(--theme-text-color-darken)',
                        borderRadius: 'var(--app-px-10)',

                    }">
                </AppInput>
                <div :style="{height:`var(--app-px-45)`}"></div>
                <div class="content-tip">
                    <p class="content-tip-p">O CPF informado desta vez deverá ser compatível com o CPF utilizado para pagamento, caso contrário o pagamento poderá falhar.</p>
                </div>
                <AppButton class="proximo" @click="() =>{
                    onClickCPF()
                }" width="95%" height="70" yellow :radius="15"
                    fontSize="15px" color="var(--theme-font-on-background-color)" background="var(--theme-primary-color)">
                    Confirmar
                </AppButton>
                <div :style="{height:`var(--app-px-20)`}"></div>
                <AppImage class="close-btn" src="/img/musicPlayer/music_close.webp"  alt="" @click="() => {
                    closePage()
                }" />
                
            </div>
    </van-popup>
</van-config-provider>

</template>

<style lang="scss" scoped>
//@import '../theme/mixin.scss';
.idIput{
    margin-left: 20px;
}

.idIputPIX{
    margin-left: 20px;
    margin-bottom: 50px;
}

.tip-poup-content{
    color: var(--theme-text-color-darken);
    font-size: 20px;
    text-align: left;
    margin-left: 22px;
    margin-top: 50px;
    line-height: 1.5;
}
.bindCPFInfo-content-header-popover {
    width: 660px;
    height: 150px;

}

.tip-poup {
    width: 750px;
    height: 100vh;
    display: flex;
    align-items: center;
    //flex-direction: column;
    position: absolute;
    justify-content: center;
    overflow: auto;

    //background-color: #0ED1F4;
    .content {
        width: 710px;
        //height: 'var(--app-px--${divHeight})';
        border-radius: 15px;
        background-color: var(--theme-main-bg-color);
        border: 2px var(--theme-text-color-lighten) solid;
        text-align: center;
        label {
            position: relative;
            top: 20px;
            color: var(--theme-text-color-darken);
            font-size: 28px;
        }

        .levantamento {
            width: 660px;
            margin: 100px 0 0 30px;
            //margin-top: 80px;
            text-align: left;
            color: var(--theme-text-color-darken);
            font-size: 20px;
            line-height: 1.5;
            display: flex;

            .Senha {
                color: var(--theme-text-color-lighten);
                position: relative;
                width: 270px;
                text-align: right;
                font-size: 22px;
            }
        }

        .proximo {
            margin: 40px 25px 30px 20px;
        }

        .passwordInput {
            position: relative;
            top: 40px;
            //scale: 0.9;
        }
        .content-tip{
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .content-tip-p {
            text-align: left;
            width: 660px;
            color: var(--theme-text-color-darken);
            font-size: 24px;
        }
    }

    .tip {
        padding-top: 30px;
        font-size: 24px;
        color: var(--theme-text-color-lighten);
        //          display: block;
        text-align: center;

    }

    // .content {
    //     //width: 100%;
    //     height: 180px;
    //     position: relative;
    //     margin: 20px 40px;
    //     text-align: center;
    //     font-size: 21px;
    //     line-height: 30px;
    //     color: white;
    // }

    .close-btn {
        position: absolute;
        width: 56px;
        height: 56px;
        left: 45%;
        margin-top: 30px;
        //background-color: aqua;
        //top: 500px;
    }
}



</style>


<style lang="scss">
:root:root {
    .bindCPFInfo-content-header-popover .van-popover__action {
        color: var(--theme-text-color-lighten);
        font-size: 24px;
        border-style: none;
        align-items: left;
        width: 660px;
    }

    // --van-popover-action-width: 660px;
    .bindCPFInfo-content-header-popover .van-popover--light {
        background: var(--theme-main-bg-color) !important;
    }

    .bindCPFInfo-content-header-popover .van-popover__content {
        background: var(--theme-main-bg-color) !important;
        border-color: var(--theme-color-line);
        border-width: 2px;
        border-radius: 14px;
        border-style: solid;
    }

    .bindCPFInfo-content-header-popover .van-popover__action {
        color: var(--theme-text-color-lighten);
        font-size: 24px;
        border-style: none;
        align-items: left;
    }

    .bindCPFInfo-content-header-popover .van-popover__action-text {
        justify-content: left
    }

    .bindCPFInfo-content-header-popover .van-popover__action:active {
        background-color: var(--theme-text-color-lighten);
    }

    .bindCPFInfo-content-header-popover .van-hairline--bottom:after {
        border-bottom-width: 0px;
    }
}
</style>
