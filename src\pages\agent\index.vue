<script setup lang='ts' name='agente'>
import Desempenho from './desempenho.vue';
import LinkDeConvite from './linkDeConvite.vue';
import MeusDados from './meusDados.vue';
import TodosOsDados from './todosOsDados.vue';

import AppComissao from './appComissao.vue';
import DadosDoSub from './appDadosDoSu.vue';
import ApostasDosSub from './appApostasDosSub.vue';


const router = useRouter()
const appStore = useAppStore()
const { isApp } = storeToRefs(appStore)
//隐藏底下菜单
appStore.setFooterDialogVisble(false)
const currentType = ref('0')
const SECLET_TYPE = readonly({
    Convite: 0,
    MenuDados: 1,
    TodosOsDados: 2,

    Desempenho:3,
    Comissão:4,
    Dados_do_Subordinado:5,

    Apostas_dos_Subordinados:6,


})

const secletType = ref(SECLET_TYPE.Convite);

if(router.currentRoute.value.query.tab !=null ){
    secletType.value= Number(router.currentRoute.value.query.tab)
}


const tabData = ref([
    {
        label: 'Link de convite',
        value: SECLET_TYPE.Convite
    },
    {
        label: 'Meus dados',
        value: SECLET_TYPE.MenuDados
    },
    {
        label: 'Todos os dados',
        value: SECLET_TYPE.TodosOsDados
    },
    //未开发
    {
        label: 'Desempenho',
        value: SECLET_TYPE.Desempenho
    },
    {
        label: 'Comissão',
        value: SECLET_TYPE.Comissão
    },
    {
        label: 'Dados do Subordinado',
        value: SECLET_TYPE.Dados_do_Subordinado
    },
    {
        label: 'Apostas dos Subordinados',
        value: SECLET_TYPE.Apostas_dos_Subordinados
    },
])

const onTabChange = () => {
    // showToast(secletType.value)
    // secletType.value = 
    // router.push("/agent/?tab="+secletType.value)
}



// watch(router.currentRoute, () => {
//     if(router.currentRoute.value.query.tab !=null ){
//         secletType.value= Number(router.currentRoute.value.query.tab)
//     }
// });


//返回
function clickLeft(){
    appStore.setFooterDialogVisble(true)
}



//跳转
function mussDadosClick(data:any){
    console.log(data)
    secletType.value= data
}

</script>
<template>
  <div class="agente">
    <AppPageTitle left-arrow title="Convidar" title-weight="700"  @clickLeft="clickLeft"/>
    <div class="tab">
        <AppTab :list-data="tabData" v-model="secletType" @change="onTabChange"></AppTab>
    </div>
    <div class = "agente-content" style=" overflow: auto;">
        <LinkDeConvite v-if="secletType == SECLET_TYPE.Convite" />
        <MeusDados v-else-if="secletType == SECLET_TYPE.MenuDados"  @clickDados="mussDadosClick" />
        <TodosOsDados v-else-if="secletType == SECLET_TYPE.TodosOsDados" />
        <Desempenho v-else-if="secletType == SECLET_TYPE.Desempenho" />
        <AppComissao v-else-if="secletType == SECLET_TYPE.Comissão" />
        <DadosDoSub v-else-if="secletType == SECLET_TYPE.Dados_do_Subordinado" />
        <ApostasDosSub v-else-if="secletType == SECLET_TYPE.Apostas_dos_Subordinados" />
    </div>

  </div>
</template>

<style lang='scss' scoped>
.agente{
    .agente-content{
        position: relative;
        min-height:calc(100vh - 163px);
        background-color: var(--theme-bg-color);
    }
}


</style>

<route lang="yaml">
    meta:
      auth: true
  </route>
  