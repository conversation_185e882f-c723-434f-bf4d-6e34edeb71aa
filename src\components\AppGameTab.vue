<script setup lang="ts" name="app-game-tab">
// import { GameHallTopEnum } from '~/types/common';
import { GameNewHallTopEnum } from "~/types/common";

const props = defineProps({
  text: String,
  scrollIconId: String,
});

const router = useRouter();
const route = useRoute();
const { id } = route.params;

//是否登录
const appStore = useAppStore();
const { isLogin } = storeToRefs(appStore);

const clickId = ref(+id);

// 获取根元素，用于访问主页面组件
const rootEl = ref(null);

const menus = [
  {
    path: "/",
    id: GameNewHallTopEnum.Popular,
    cateName: "Popular",
    scrollTo: "popular",
  },
  {
    path: "/subgame",
    id: GameNewHallTopEnum.Slots,
    cateName: "Slots",
    scrollTo: "slots",
  },
  {
    path: "/Blockchain",
    id: GameNewHallTopEnum.Blockchain,
    cateName: "Blockchain",
    scrollTo: "Blockchain",
  },
  {
    path: "/AoVivo",
    id: GameNewHallTopEnum.AoVivo,
    cateName: "Ao Vivo",
    scrollTo: "AoVivo",
  },
  {
    path: "/Pescaria",
    id: GameNewHallTopEnum.Pescaria,
    cateName: "Pescaria",
    scrollTo: "Pescaria",
  },

  // {
  //   path: "/subgame/recente",
  //   id: GameNewHallTopEnum.Recente,
  //   cateName: "Recente",
  //   scrollTo: "recente",
  // },
  // {
  //   path: "/subgame/favoritos",
  //   id: GameNewHallTopEnum.Favoritos,
  //   cateName: "Favoritos",
  // },
];

const liClick = (item: any) => {
  console.log(JSON.stringify(item));
  clickId.value = item.id;

  // 如果有scrollTo属性，则尝试滚动到指定位置
  if (item.scrollTo) {
    // 如果不在主页，先导航到主页
    if (route.path !== "/") {
      router.push("/");
      // 添加一个小延迟，确保页面加载完成后再滚动
      setTimeout(() => {
        scrollToSection(item.scrollTo);
      }, 300);
    } else {
      // 已经在主页，直接滚动
      scrollToSection(item.scrollTo);
    }
    return;
  }

  return;
  // 其他菜单项保持原有行为
  if (item.path) {
    router.push(item.path);
    return;
  }
  router.push(`/game-list/${item.id}`);
};

// 滚动到指定位置
const scrollToSection = (sectionId: string) => {
  // 使用自定义事件触发滚动，避免直接访问内部属性
  window.dispatchEvent(
    new CustomEvent("scrollToSection", {
      detail: { sectionId },
    })
  );
};

watch(
  route,
  (val, old) => {
    if (val.path === "/") {
      clickId.value = GameNewHallTopEnum.Popular;
      return;
    }
    if (val.path === "/subgame/recente") {
      clickId.value = GameNewHallTopEnum.Recente;
      return;
    }
    if (val.path === "/subgame/favoritos") {
      clickId.value = GameNewHallTopEnum.Favoritos;
      return;
    }

    // if (val.path === '/Slots'){
    //   clickId.value = GameNavEnum.Slots
    //   return
    // }
    // if (val.path.indexOf('/game-list/') === -1) {
    //   clickId.value = -1
    // } else {
    //   clickId.value = +route.params.id
    // }
  },
  { immediate: true }
);

// 监听 scrollIconId 的变化
watch(
  () => props.scrollIconId,
  (newVal, oldVal) => {
    if (newVal) {
      // 当 scrollIconId 变化时，可以在这里添加相应的处理逻辑
      console.log("scrollIconId changed:", newVal);
      // 例如：滚动到指定位置
      scrollToSection(newVal);
    }
  }
);

// const hh = ref(clickId.value)
</script>

<template>
  <!-- <ul class="app-game-tab">
    <li class="item" v-for="(item, i) in gameNavData" @click="liClick(item)" :key="item.id" :class="{
      active: currentActive === i
    }">
      <AppImage class="icon" :src="`/icons/game-tab${item.id}.png`" alt="" />
      <span v-html="item.name"></span>
    </li>
  </ul> -->
  <div class="app-game-tab-sec">
    <div class="left">
      <!-- <ul ref="navBoxRef" class="nav-box">
        <li class="n-item" v-for="(item, i) in gameNavData" @click="liClick(item)" :key="item.id" :class="{
          active: currentActive === i
        }">
        <AppImage class="icon" :src="`/icons/gty_${item.id}.png`" alt="" />
        </li>
      </ul> -->

      <!-- <div class="left-center" v-if="!isLogin">
        <label class="left-center-text">Pronto para jogar? Cadastre-se agora！</label>
        <div class="left-center-reg" @click="openRegisterDialog(true)">
          <label class="left-center-reg-text">Cadastre-se</label>
        </div>
      </div>

      <div class="left-center" v-if="isLogin">
        <label class="left-center-text">Deposite agora e receba bônus enormes！</label>
        <div class="left-center-reg left-center-dep" @click="$router.push('/finance')">
          <label class="left-center-reg-text">Depósito</label>
        </div>
      </div> -->

      <!-- <van-tabs ref="gameTabRef" :swipe-threshold="4" line-height="0" background="transparent" line-width="0" class="game-type-tabs" v-model:active="clickId">
        <van-tab v-for="item in gameNavData" :key=item.id   :name=item.id  >
          <template #title v-if="item.id!=GameNavEnum.Platform">
            <div :ref="(ele) => setTabRef(ele, item.id)" class="n-tab-item" @click="liClick(item)" :class="{active: clickId === item.id}">
              <AppImage class="icon" :class="`icon_${item.id}`" :src="`/icons/nav_${item.id}${clickId === item.id? '-active' : ''}.png`" alt="" />
              <div class="text" :class="{text2:item.id==GameNavEnum.Slot,active: clickId === item.id}">{{ item.cateName }}</div>
              <div class="text_line" v-if="clickId === item.id" ></div>
            </div>
          </template>
        </van-tab>
      </van-tabs> -->

      <!-- <van-tabs ref="gameTabRef" :swipe-threshold="4" line-height="0" background="transparent" line-width="0" class="game-type-tabs" v-model:active="clickId">
        <van-tab v-for="item in menus" :key=item.id   :name=item.id  >
          <template #title >
            <div :ref="(ele) => setTabRef(ele, item.id)" class="n-tab-item" @click="liClick(item)" :class="{active: clickId === item.id}">
              <AppImage class="icon" :class="`icon_${10000}`" :src="`/icons/nav_${item.id}${clickId === item.id? '-active' : ''}.png`" alt="" />
              <div class="text" :class="{text2:item.id === 10001, active: clickId === item.id}">{{ item.cateName }}</div>
              <div class="text_line" v-if="clickId === item.id" ></div>
            </div>
          </template>
        </van-tab>
      </van-tabs> -->

      <!-- ADD: New vertical navigation -->
      <div class="vertical-nav-menu">
        <div
          v-for="item in menus"
          :key="item.id"
          class="nav-item"
          :class="{ active: clickId === item.id }"
          @click="liClick(item)"
        >
         <div class="icon-container">
          <AppImage
            class="icon"
            :src="`/icons/index_menu/nav_${item.id}.png`"
            alt=""
          />
        </div>
          <span class="text">{{ item.cateName }}</span>
        </div>
      </div>

      <!-- <div>
        <a href="#setting" class="nav__link">
          12123123
        </a>
      </div>

      <div>
        <a href="#setting1" class="nav__link">
          slots
        </a>
      </div> -->

      <!-- REMOVE: <div class="line"></div>  -->

      <!-- <div ref="gameTabRef" :swipe-threshold="4" line-height="0" background="transparent" line-width="0"  class="game-type-tabs2" >
        <div v-for="item in gameNavData" :key=item.id   :name=item.id  >
          <template  v-if="item.id!=GameNavEnum.Platform">
            <div :ref="(ele) => setTabRef(ele, item.id)" class="n-tab-item" @click="liClick(item)" :class="{active: clickId === item.id}">
              <AppImage class="icon" :class="`icon_${item.id}`" :src="`/icons/nav_${item.id}${clickId === item.id? '-active' : ''}.png`" alt="" />
              <div class="text" :class="{text2:item.id==GameNavEnum.Slot}">{{ item.cateName }}</div>
            </div>
          </template>
        </div>
      </div> -->
    </div>
    <!-- <div class="search-box">
      <AppGameSearch />
    </div> -->
  </div>
</template>

<style lang="scss" scoped>
@import "../theme/mixin.scss";
.app-game-tab-sec {
  display: flex;
  // gap: 20px;
  width: 150px;
  align-items: center;
  justify-content: center;
  position: -webkit-sticky; /* Safari */
  position: sticky;
  top: 14vw;
  left: 0;
  z-index: 1;
  height: calc(40vh - 88px);
  // background-color: var(--theme-home-bg);
  // overflow-x: hidden;
  // margin-left: -20px;
  .search-box {
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    right: 10px;
    top: 2px img {
      display: block;
    }
  }

  .left {
    flex: 1;
    // overflow: hidden;
    width: 100%;
    height: 100%;
    // line-height: 52px;
    // padding-top: 310px;
    // padding-left: 15px;
    // background: var(--theme-home-bg);
    //  border-radius: 15px;
    // @include webp('/icons/appGameTab_bg.png');
    // background-size: 750px 483px;

    .left-center {
      position: absolute;
      // background-color: aqua;
      width: 345px;
      height: 300px;
      top: 10px;
      left: 20px;
      color: #ffffff;
      .left-center-text {
        width: 345px;
        height: 150px;
        line-height: 35px;
        font-size: 35px;
        text-shadow: 1px 2px 2px rgba(3, 0, 0, 0.73);
        font-family: Arial;
        font-weight: 800;
      }

      .left-center-reg {
        position: absolute;
        top: 215px;
        width: 237px;
        height: 75px;
        // background:radial-gradient(ellipse 72.22% 72.22% at 50% 50% ,#f30808 0%,#f26b04 100%);
        background-color: var(--app-red-color);
        border-radius: 14px;
        .left-center-reg-text {
          display: block;
          text-shadow: 3px 3px 3px rgba(0, 0, 0, 0.36);
          font-family: Arial;
          font-weight: 900;
          font-size: 26px;
          text-align: center;
          line-height: 75px;
        }
      }
      .left-center-dep {
        // background:radial-gradient(ellipse 72.22% 72.22% at 50% 50% ,#f30808 0%,#f26b04 100%);
        background-color: var(--app-red-color);
      }
    }

    .vertical-nav-menu {
      display: flex;
      flex-direction: column;
      width: 100%;
      align-items: center;
      gap: 16px;
      padding-top: 10px;
    }

    .nav-item {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-start;
      width: 19.666667vw;
      height:10.066667vw;
      cursor: pointer;
      border-radius: 15px;
      background: linear-gradient(to bottom, #0f7a4e 0%, #176244 100%);
      color: var(--theme-text-color-lighten);
      padding: 0 0 0 5px;
      transition: background-color 0.2s ease-in-out;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      margin-bottom: 2px;
      
      // &.active {
      //   background-color: #0e6e45;
      //   color: var(--theme-text-color-lighten);
      //   .text {
      //     color: var(--theme-text-color-lighten);
      //     font-weight: bold;
      //   }
      // }

      &:first-child {
        position: relative;
        &::before {
          content: "";
          position: absolute;
          top: 10px;
          left: -8px;
          width: 20px;
          height: 28px;
          background-image: url('/icons/fire.png');
          background-size: contain;
          background-repeat: no-repeat;
        }
      }
      .icon-container {
        width: 8vw;
        // flex: 1; 
      }
      .icon {
        width:100%;
 
        // height: 40px;
        // margin-right: 10px;
        
      }

      .text {
        font-size: 16px;
        display:inline-block;
        width:100px;
        margin-right:auto;
        color: var(--theme-text-color-lighten);
        text-align: center;
      }
    }
  }
  .nav-box {
    display: flex;
    gap: 20px;
    flex-wrap: nowrap;
    height: 103px;
    overflow-x: scroll;

    .n-item {
      flex: 1;
      flex-basis: auto;
      width: 84px;
      height: 84px;
      flex-shrink: 0;
      border-radius: 10px;
      background: #0b1c3d;

      &.active {
        background: #f5c31b;
      }

      img {
        width: 78px;
        height: 78px;
        display: block;
        margin-top: 5px;
        margin-left: 5px;
      }
    }
  }
}

.line {
  width: 100%;
  height: 1px;
  background-color: var(--theme-color-line);
  position: absolute;
  bottom: 0px;
}

.app-game-tab {
  height: 106px;
  flex-shrink: 0;
  border-radius: 20px;
  background: #033377;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(2, 1fr);
  overflow: hidden;

  .item {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    color: #fff;
    line-height: 24px;
    font-size: 26px;
    padding-left: 14px;

    &.active {
      // background: linear-gradient(358deg, #1373EF 0%, #0ED1F4 100%), linear-gradient(180deg, #044B9A 0%, #011A51 100%);
    }

    .icon {
      width: 50px;
      margin-right: 6px;
    }
  }
}

//使用dev的tab
.game-type-tabs2 {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  overflow: auto;
}

.game-type-tabs2::-webkit-scrollbar {
  width: 10px; /* 垂直滚动条的宽度 */
  height: 10px; /* 水平滚动条的高度 */
}

/* 定义滚动条轨道样式 */
.game-type-tabs2::-webkit-scrollbar-track {
  background: #f1f1f1; /* 轨道的背景颜色 */
}

/* 定义滚动条滑块样式 */
.game-type-tabs2::-webkit-scrollbar-thumb {
  background: #888; /* 滑块的背景颜色 */
}

/* 定义当滑块悬停或活动时的样式 */
.game-type-tabs2::-webkit-scrollbar-thumb:hover {
  background: #555; /* 滑块在悬停状态下的背景颜色 */
}
</style>
