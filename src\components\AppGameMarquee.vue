<script setup lang="ts" name="app-game-tab">
import router from "~/router";
import speakerIcon from "/icons/svg/speaker.svg?raw";
import emailIcon from "/icons/svg/email.svg?raw";
//三个走马灯节点
const boxPrompt1 = ref();
const boxPrompt2 = ref();
const boxPrompt3 = ref();
//
const boxPrompt1_img = ref(0);
const boxPrompt1_test = ref("");
const boxPrompt2_img = ref(0);
const boxPrompt2_test = ref("");
const boxPrompt3_img = ref(0);
const boxPrompt3_test = ref("");
// let intervalId = 0
const marqueeData = ref<any>([]); //ref(-1) //已经领取到第几位
const timeout = ref();
//偏移位置
let initLeft1 = 0;
let initLeft2 = 0;
let initLeft3 = 0;
let marqueeWidth = window.innerWidth - 50; //340
let curInitPos = -1; //初始化位置
let curPos = 0;

//事件监听
onMounted(() => {
  console.log("appGameMarquee加载");
});

onBeforeUnmount(() => {
  console.log("appGameMarquee加载44");
  clearTimeout(timeout.value);
});

const { run: runGetBroadCast } = useRequest(ApiGetBroadCast, {
  manual: true,
  onSuccess: (data) => {
    if (data) {
      marqueeData.value = [...data];

      setMargueeData(1);
      setMargueeData(2);
      setMargueeData(3);

      moveBoxPrompt();
    } else {
      //取不到 60秒后重新取数据
      clearTimeout(timeout.value);
      timeout.value = setTimeout(() => {
        runGetBroadCast();
      }, 60 * 1000);
    }
  },
});

runGetBroadCast();

//设置数据
const setMargueeData = (type: number) => {
  curInitPos++;
  if (curInitPos >= marqueeData.value.length) {
    curInitPos = 0;
  }

  if (type == 1) {
    boxPrompt1_img.value = marqueeData.value[curInitPos].type;
    boxPrompt1_test.value = marqueeData.value[curInitPos].content;
  } else if (type == 2) {
    boxPrompt2_img.value = marqueeData.value[curInitPos].type;
    boxPrompt2_test.value = marqueeData.value[curInitPos].content;
  } else if (type == 3) {
    boxPrompt3_img.value = marqueeData.value[curInitPos].type;
    boxPrompt3_test.value = marqueeData.value[curInitPos].content;
  }

  console.log("初始化======第=" + type + " 数据第" + curInitPos);
};

const moveBoxPrompt = () => {
  setTimeout(() => {
    //先初始化位子
    boxPrompt1.value.style = "transform: translateX(" + initLeft1 + "px)";
    initLeft2 = boxPrompt1.value.offsetWidth + 30;
    boxPrompt2.value.style = "transform: translateX(" + initLeft2 + "px)";

    initLeft3 =
      boxPrompt1.value.offsetWidth + boxPrompt2.value.offsetWidth + 30;
    boxPrompt3.value.style = "transform: translateX(" + initLeft3 + "px)";

    // 开始跑
    const intervalId = setInterval(() => {
      moveBoxPromptMore(boxPrompt1, 1, intervalId);
      moveBoxPromptMore(boxPrompt2, 2, intervalId);
      moveBoxPromptMore(boxPrompt3, 3, intervalId);
    }, 16);
  }, 100);
};

const moveBoxPromptMore = (boxPrompt: any, type: number, intervalId: any) => {
  let initLeft = 0;
  if (type == 1) {
    initLeft = initLeft1;
  } else if (type == 2) {
    initLeft = initLeft2;
  } else if (type == 3) {
    initLeft = initLeft3;
  }

  initLeft = initLeft - 0.8;

  //判断节点是否存在
  if (boxPrompt && boxPrompt.value && boxPrompt.value.offsetWidth) {
    if (initLeft < -boxPrompt.value.offsetWidth) {
      //初始化
      setMargueeData(type);
      //计算位置
      let lastNode = type - 1; //最后一个节点位置
      lastNode = lastNode == 0 ? 3 : lastNode;

      if (lastNode == 1) {
        curPos = boxPrompt1.value.getBoundingClientRect().left;
        curPos += boxPrompt1.value.offsetWidth;
      } else if (lastNode == 2) {
        curPos = boxPrompt2.value.getBoundingClientRect().left;
        curPos += boxPrompt2.value.offsetWidth;
      } else if (lastNode == 3) {
        curPos = boxPrompt3.value.getBoundingClientRect().left;
        curPos += boxPrompt3.value.offsetWidth;
      }
      if (curPos > marqueeWidth) {
        initLeft = curPos;
      } else {
        initLeft = marqueeWidth;
      }
      // console.log(" ====="+type +" "+curPos)
    }
    boxPrompt.value.style = "transform: translateX(" + initLeft + "px)";
    if (type == 1) {
      initLeft1 = initLeft;
    } else if (type == 2) {
      initLeft2 = initLeft;
    } else if (type == 3) {
      initLeft3 = initLeft;
    }
  } else {
    clearInterval(intervalId); //取消定时器
    console.log("卸载=====setInterval");
  }
};

//邮件界面
const mail = () => {
  router.push("/mensagens/?tab=1");
};
</script>

<template>
  <div class="content">
    <div class="center">
      <!-- <AppImage class="icon" src="/icons/marquee.webp" alt="" /> -->
      <!-- <img  src="/icons/icon_dt_pmd.png"  class="icon"    /> -->
      <div class="icon" v-html="speakerIcon"></div>
      <div class="marquee-prompt">
        <div class="list-prompt" ref="boxPrompt1">
          <div class="list-item">
            <!-- <AppImage class="list-item-icon" :src="`/icons/marquee_${boxPrompt1_img}.webp`" alt="" /> -->
            <span class="prompt">{{ boxPrompt1_test }}</span>
          </div>
        </div>

        <div class="list-prompt" ref="boxPrompt2">
          <div class="list-item">
            <!-- <AppImage class="list-item-icon" :src="`/icons/marquee_${boxPrompt2_img}.webp`" alt="" /> -->
            <span class="prompt">{{ boxPrompt2_test }}</span>
          </div>
        </div>

        <div class="list-prompt" ref="boxPrompt3">
          <div class="list-item">
            <!-- <AppImage class="list-item-icon" :src="`/icons/marquee_${boxPrompt3_img}.webp`" alt="" /> -->
            <span class="prompt">{{ boxPrompt3_test }}</span>
          </div>
        </div>
      </div>
      <!-- <AppImage
        class="mail"
        src="/icons/marquee_mail.webp"
        alt=""
        @click="mail"
      /> -->
      <div class="mail" v-html="emailIcon"></div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@import "../theme/mixin.scss";
.content {
  width: 100%;
  height: 55px;
}

.center {
  margin: 0 auto;
  width: 710px;
  height: 40px;
  // background:#2c2031;
  // border:2px solid;
  // border-color:#b01ffa;
  // border-radius:10px;
}

.icon {
  display: inline-block;
  // position: absolute;
  width: 37px;
  position: relative;
  top: -7.5px;
  
}
.mail {
  display: inline-block;
  position: absolute;
  width: 45px;
  float: right;
  margin-top: -0px;
}

.marquee-prompt {
  display: inline-block;
  width: calc(100% - 110px);
  overflow: hidden;
  position: relative;
  height: 50px;
  // background-color: #3c383d;
  border-radius: 10px;
  margin-left: 15px;
  margin-right: 15px;
}

//

.list-prompt {
  margin-top: 5px;
  position: absolute;
  white-space: nowrap;
  // background-color: #b01ffa;
}

.list-item {
  display: inline;
  margin-right: 30px;

  .list-item-icon {
    display: inline-block; /* 将元素设置为行内块元素 */
    vertical-align: middle; /* 垂直居中对齐 */
    width: 44px;
  }
  .prompt {
    display: inline-block; /* 将元素设置为行内块元素 */
    vertical-align: middle; /* 垂直居中对齐 */
    color: var(--theme-text-color);
    font-size: 24px;
    // width: 250px;
    overflow: hidden;
    white-space: nowrap;
    // text-overflow: ellipsis;
    user-select: none;
    padding-left: 6px;
  }
}
</style>

<!-- <script setup lang="ts" name="app-game-tab">
  const boxPrompt = ref()
  // let intervalId = 0
  const marqueeData = ref<any>([])  //ref(-1) //已经领取到第几位
  const isStop= ref(false) 
  const timeout = ref();
  let initLeft =0

  onMounted(() => {
      console.log("appGameMarquee加载")
  })

  onBeforeUnmount(() => {
    console.log("appGameMarquee加载44")
    clearTimeout(timeout.value);
  })


const { run: runGetBroadCast } = useRequest(ApiGetBroadCast, {
    manual: true,
    onSuccess: (data) => {
      if (data) {
        marqueeData.value=[...data]
        isStop.value  =false
        moveBoxPrompt()
        
      }else{
        //取不到 60秒后重新取数据
        clearTimeout(timeout.value);
        timeout.value = setTimeout(() => {
          runGetBroadCast()
        }, 60*1000);
      } 
    }
  })

runGetBroadCast()


const moveBoxPrompt=()=>{
  
    
    boxPrompt.value.style = 'transform: translateX('+ initLeft +'px)'
    let init = true
   
    const intervalId = setInterval(() => {
      if(!init){
        initLeft --
        if(boxPrompt && boxPrompt.value && boxPrompt.value.offsetWidth){
          if(!isStop.value){
            if( initLeft < (-boxPrompt.value.offsetWidth)){
            initLeft = window.innerWidth-50 //340
            console.log("initLeft=="+initLeft)
            isStop.value =true
            runGetBroadCast()
            clearInterval(intervalId ); //取消定时器
          }
           boxPrompt.value.style = 'transform: translateX('+ initLeft +'px)'
          }
        }else{
          clearInterval(intervalId );//取消定时器
          console.log("卸载=====setInterval")
        }
      }
      init = false
  },16)
}

</script>

<template>
 
  <div class="content">
    
    <div class="center" >
      <AppImage class="icon" src="/icons/marquee.webp" alt="" />
      <div class="marquee-prompt">
        <div class="list-prompt" ref='boxPrompt' >
          <div class="list-item" v-for=" (item ,index) in marqueeData " :title="item" :id="`itemMarquee_${index}`" >
            <AppImage class="list-item-icon" :src="`/icons/marquee_${item.type}.webp`" alt="" />
            <span  class="prompt">{{item.content}}</span> 
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<style lang="scss" scoped>
@import '../theme/mixin.scss';
.content {
  width: 100%;
  height:60px;
}

.center {
  margin: 0 auto;
  width:714px;
  height:60px;
  background:#2c2031;
  border:2px solid;
  border-color:#b01ffa;
  border-radius:10px;
 
}

.icon{
  width: 60px;
}


.marquee-prompt {
  display: inline-block;
  width: calc(100% - 70px);
  overflow: hidden;
  position: relative;
  height: 60px;
  // background-color: #3c383d;
  border-radius:10px;
  margin-left: 5px;
}
  
// 

.list-prompt {
  margin-top: 5px;
  position: absolute;
  white-space: nowrap;
}
 
.list-item{
  display:  inline;
  margin-right: 30px;

}


.list-item-icon{
  display: inline-block; /* 将元素设置为行内块元素 */
  vertical-align: middle; /* 垂直居中对齐 */
  width: 44px;
}



.prompt {
  display: inline-block; /* 将元素设置为行内块元素 */
  vertical-align: middle; /* 垂直居中对齐 */

  font-size: 30px;
  color:#ffffff;
  font-size:24px;
  // width: 250px;
  overflow: hidden;
  white-space: nowrap;
  // text-overflow: ellipsis;
  user-select: none;
 
  
  // cursor: pointer;
  // &:hover {
  //   color: #68aef8;
  // }
  // &:active{
  //   color: #1890ff;
  // }
}

</style> -->
