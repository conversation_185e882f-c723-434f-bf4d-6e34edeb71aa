<script setup lang="ts" name="app-index-game-container">
import { GameHallTopEnum } from "~/types/common";

// 分页的列表数据
const gameList = ref<any[]>([]);
const isShowVertical = ref(false); //切换横竖版
const isShowThreeRow = ref(false); //是否显示三行
isShowVertical.value = true;

const appStore = useAppStore();
const { isSmallGameIcon } = storeToRefs(appStore);

// 定义props
interface Props {
  showTitle?: boolean; // 添加showTitle属性
}

const props = withDefaults(defineProps<Props>(), {
  showTitle: true // 默认显示标题
});

//取数据
getData();

function getData() {
  let recenteData = window.localStorage.getItem("recenteData");
  if (recenteData) {
    const loadedMap = JSON.parse(recenteData);
    gameList.value = loadedMap;
  }
}

function appGamePlatformby() {}
const gameItemInnerStyle: any = {
  margin: isSmallGameIcon.value
    ? `var(--app-npx-22) var(--app-npx-20)`
    : `var(--app-npx-15) 0`,
};
</script>

<template>
  <div class="app-index-game-container">
    <!-- 根据showTitle属性控制是否显示标题 -->
    <AppIndexTitle
      v-if="showTitle"
      :id="3"
      :platform_id="GameHallTopEnum.Recente"
      :isCallback="true"
      @appGamePlatform-by="appGamePlatformby"
    />

    <div class="app-maps game-container">
      <div class="no-data-center" v-if="!gameList || gameList.length === 0">
        <svg
          t="1745852083452"
          class="icon"
          viewBox="0 0 1127 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="100479"
          width="100"
          height="100"
        >
          <path
            d="M944.3 565.594L821.966 367.53c-2.33-4.66-6.408-7.574-11.068-7.574h-506.23c-4.66 0-8.737 2.33-11.068 7.574L171.267 565.594c-1.166 1.165-1.166 2.33-1.166 4.66v242.338c0 7.573 4.66 12.233 12.234 12.233h751.48c7.572 0 12.233-4.66 12.233-12.233V570.254c-0.583-1.747-0.583-2.912-1.748-4.66zM312.242 385.006h491.665l110.1 173.597H643.709c-7.573 0-12.233 4.66-12.233 12.234 0 40.195-33.205 73.4-73.4 73.4-40.196 0-73.4-33.205-73.4-73.4 0-7.573-4.661-12.234-12.234-12.234H201.559l110.683-173.597zM920.998 800.94h-726.43V583.07h266.805c6.408 47.768 47.768 85.633 96.702 85.633s90.876-37.865 96.702-85.634H921.58v217.87zM113.595 685.598v-13.399c0-3.495-2.913-6.408-6.408-6.408-3.495 0-6.408 2.913-6.408 6.408v13.399H87.38c-3.495 0-6.408 2.912-6.408 6.408 0 3.495 2.913 6.408 6.408 6.408H100.78v13.398c0 3.495 2.913 6.408 6.408 6.408 3.495 0 6.408-2.913 6.408-6.408v-13.398h13.398c3.496 0 6.408-2.913 6.408-6.408 0-3.496-2.912-6.408-6.408-6.408H113.595zM992.069 420.54h20.389c5.242 0 9.903 4.66 9.903 9.903 0 5.826-4.078 9.903-9.903 9.903h-20.39v20.39c0 5.242-4.66 9.903-9.903 9.903s-9.903-4.078-9.903-9.904v-20.389h-20.389c-5.243 0-9.903-4.66-9.903-9.903 0-5.825 4.078-9.903 9.903-9.903h20.39v-20.39c0-5.242 4.66-9.902 9.902-9.902 5.826 0 9.904 4.077 9.904 9.903v20.389z m71.652 86.799h12.816c3.495 0 6.408 2.912 6.408 6.408 0 3.495-2.913 6.408-6.408 6.408h-12.816v12.816c0 3.495-2.912 6.407-6.408 6.407-3.495 0-6.408-2.912-6.408-5.825v-12.816H1038.09c-3.495 0-6.408-2.913-6.408-6.408 0-3.495 2.913-6.408 6.408-6.408h12.816v-13.398c0-3.496 2.913-6.408 6.408-6.408 3.496 0 6.408 2.912 6.408 6.408v12.816zM130.49 404.23v-20.39c0-5.825-4.66-9.903-9.904-9.903-5.825 0-9.903 4.66-9.903 9.904v19.806H90.876c-5.826 0-9.904 4.66-9.904 9.903 0 5.826 4.66 9.904 9.904 9.904h19.806v19.806c0 5.825 4.66 9.903 9.903 9.903 5.826 0 9.904-4.66 9.904-9.903v-19.806h19.806c5.825 0 9.903-4.66 9.903-9.904 0-5.825-4.66-9.903-9.903-9.903 0 0.583-19.806 0.583-19.806 0.583z m-90.294 198.647c-22.137 0-40.196-18.06-40.196-40.196s18.059-40.195 40.196-40.195c22.136 0 40.195 18.059 40.195 40.195 0 22.72-18.059 40.196-40.195 40.196z m0-19.807c11.068 0 19.806-8.738 19.806-19.806s-8.738-19.807-19.806-19.807-19.807 8.738-19.807 19.807 8.738 19.806 19.807 19.806zM1109.16 471.805c-9.904 0-18.642-8.156-18.642-18.642s8.156-18.641 18.642-18.641c9.903 0 18.64 8.155 18.64 18.641 0 10.486-8.155 18.642-18.64 18.642z m0-8.739c5.242 0 9.32-4.077 9.32-9.32s-4.078-9.32-9.32-9.32c-5.243 0-9.321 4.077-9.321 9.32s4.66 9.32 9.32 9.32zM298.843 170.048c8.156-8.156 21.554-8.156 29.71 0l74.565 73.983c8.156 8.155 8.156 21.554 0 29.71-8.155 8.155-21.554 8.155-29.71 0l-74.565-73.984c-8.155-8.155-8.155-21.554 0-29.71z m259.232-37.283c11.65 0 20.971 9.32 20.971 20.972v104.857c0 11.651-9.32 20.972-20.971 20.972s-20.972-9.321-20.972-20.972V153.737c-0.582-11.069 9.32-20.972 20.972-20.972-0.583 0 0 0 0 0z m259.813 32.04c8.156 8.155 8.156 21.554 0 29.71l-73.982 73.982c-8.156 8.156-21.555 8.156-29.71 0-8.156-8.155-8.156-21.554 0-29.71l73.983-73.982c8.155-8.156 21.554-8.156 29.71 0z"
            p-id="100480"
            fill="#cdcdcd"
          ></path>
        </svg>
        <span style="color: gray;">No data</span>
      </div>
      <div
        class="content"
        :class="{
          versionContent: isShowVertical,

          'is-empty-loading': !gameList?.length,
        }"
        :style="gameItemInnerStyle"
      >
        <template v-if="gameList && gameList.length > 0">
          <AppGameItem
            v-if="isSmallGameIcon"
            class="game-item"
            v-for="(item, idx) in gameList"
            :key="item.id + idx"
            :data="{ ...item }"
          />
          <AppGameItemBig
            v-else
            class="game-item"
            v-for="(item, idx) in gameList"
            :key="item.id + idx"
            :data="{ ...item }"
          />
          
        </template>
        
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.app-index-game-container {
  color: var(--app-title-color);
  font-size: var(--app-gameTitle-fontSize);
  // font-weight: 700;
  padding: 26px 0;
  line-height: 40px;
  margin-bottom: 150px;
}
.app-maps.game-container {
  margin-left: -20px;
  margin-right: -20px;
  border-radius: 0;
  padding-left: 30px;
  padding-right: 30px;
}
.app-maps {
  // border-radius: 0px 30px 30px 30px;
  // background: linear-gradient(180deg, #044B9A 0%, #011A51 100%), #1B2E1B;
  // padding-top: 17px;
  // padding: 0 10px;
  // padding-bottom: 8px;

  .content {
    // height: 590px;
    // padding-bottom: 8px;
    // height: calc(100vh - 360px);
    display: grid;
    flex-wrap: wrap;
    // grid-auto-flow: row;
    // grid-column-gap: 10px;
    // grid-row-gap: 10px;
    grid-template-columns: repeat(3, 1fr);
    scroll-snap-type: x;
    overflow: auto hidden;
    height: 100%;
    // grid-template-columns: repeat(auto-fill, 200px);
    min-height: 350px;
    &::-webkit-scrollbar {
      display: none;
    }
    &.one-row {
      grid-template-rows: auto;
      justify-content: flex-start;
      // height: 295px;
      // height: 295px;
    }
    .left {
      width: 12px;
      scroll-snap-align: start;
    }
    // .left {
    //   width: 12px;
    //   scroll-snap-align: start;
    // }
    &.is-empty-loading {
      align-items: center;
      justify-items: center;
      justify-content: center;
    }

    &.overflow-hidden {
      overflow: hidden;
    }
    // &.flow-row {
    //   grid-auto-flow: row;
    // }

    .last-loading {
      grid-row-start: 1;
      grid-row-end: 2;
      width: 580px;
      height: 100%;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .more {
    // justify-items: center;
    // justify-content: center;
    text-align: center;

    border-radius: 10px;
    .more-text {
      display: block;
      color: var(--theme-text-color-lighten);
      font-size: 24px;
    }

    .more-text-carregar {
      display: block;
      position: absolute;
      margin: 0 auto;
      text-align: center;
      color: var(--theme-text-color);
      font-size: 24px;
      transform: translate(-50%, -50%);
      left: 50%;
      top: 50;
      margin-top: 5px;
    }

    .more-text-img {
      width: 14px;
    }
  }

  //热门竖版显示
  .versionContent {
    // grid-template-columns: repeat(4, 1fr); /* 创建三个等宽的列 */
    // grid-template-rows: repeat(4, 230px); /* 创建两行固定高度为100px的网格 */
  }
}
.no-data-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px; // 可根据实际情况调整高度
}
</style>
<route lang="yaml">
meta:
  layout: home
</route>
