import i18n from "~/lang"
import { CycleModeEnum, PGSlotsImgTypeEnum } from '~/types/common';

export const useAppStore = defineStore('app', () => {
  const language = ref(Languages.PT_BR)
  const loginDialogVisible = ref(false)
  const loginDialogType = ref('login')
  const registerDialogVisible = ref(false)
  const findPasswordDialogVisible = ref(false)
  const editAvatarDialogVisble = ref(false)
  const token = ref(localStorage.getItem('t') || '');
  const isLogin = computed(() => token.value ? true : false)
  const isApp = ref(false) // 是否App引用
  const pay_password = ref("0")//支付密码
  const findPayPasswordDialogVisible = ref(false)
  const adicionarPixDialogVisible = ref(false)
  const storedAccount = ref(localStorage.getItem('sc') || ''); //记住密码保存的账户
  const activeDialogVisble = ref(true)//广告图片
  const onceActiveDialogVisble = ref(true)//广告图片
  const appFooterVisible = ref(true)  //进入三方游戏底部菜单了隐藏
  const isRefreshShop = ref(false)  //是否刷新商城
  const isShowLoading = ref(false) //是否显示全局的loading
  const isShowMessage = ref(false)  //金币提升框
  const showMessage = ref("")
  const iosInfoShow = ref(true)
  const isShowGoldCollect = ref(false)
  const showLeftMenu = ref(false)//是否显示左侧菜单
  const curCycleMode = ref(CycleModeEnum.Sequential_Loop);
  const isPlayingMusic = ref(false); // 用于追踪播放状态
  const isShowPay = ref(false); //
  const isShowPayRecords = ref(false); //
  const userVipInfo = ref<any>()
  const showMusicPlayer = ref(false)
  const showLoginOut = ref(false) //退出登录确认弹框
  const showRecharge = ref(false) //跳转充值页面弹窗
  const secletType = ref(0)//活动类型
  const privacyShow = ref(false) //用户协议
  const isShowBonusMessage = ref(false) //领取奖励弹窗
  const appRightActiveVisible = ref(true) //右侧悬浮图总开关
  const isShowAppBaixar = ref(true) //领取奖励弹窗
  const isSmallGameIcon = ref(true) //是否是小图标

  const bindCPFInfoVisible = ref(false)//是否绑定CPF信息
  const constCPFInfo = ref("")
  const iconType = ref(1) //
  const isFirstChargePopup = ref(false); //是否首充弹窗
  const isShowStar  = ref(true) //显示星星
  const isShowGood = ref(true) //显示赞
  const isShowPGSlots = ref(false) //显示PGSlots
  const PGSlotsImgType = ref(PGSlotsImgTypeEnum.r7) //

  const gameItemPageNum = computed(() => {
    return isSmallGameIcon.value ? 12 : 9;
  }) 
  const getRemoteImgUrl = computed(() =>{
    return getBrazilImg(isSmallGameIcon.value,iconType.value)
  })
  
  const { run: runGetMemberInfo, data: memberInfoData, loading: loadingUserinfo } = useRequest(ApiGetMemberInfo, {
    ready: isLogin,
    onSuccess: () => {
      runGetUserBalance()
      runDepositBonusConf()
      runGetBalanceDetail()
      runGetWithdrawConf()
      runGetUserBanks()
      // runGetTurnTableInfo()  转盘暂时不需要
      runGetTurnTableSwitch()
      if (memberInfoData.value) {
        pay_password.value = memberInfoData.value.pay_password
      }
    },
    onError: (err) => {
      console.error('获取用户信息失败', err)
      clearToken();
    }
  })

  //获取广告弹出框数据
  const { run: runActivePopupData } = useRequest(ApiGetActivepopup, {
    manual: true,
    onSuccess(data) {
      if (data && data.length > 0 && onceActiveDialogVisble.value == true) {
        setActiveDialogVisble(true)
        onceActiveDialogVisble.value = false
      }

    }
  })



  const { run: runGetUserBalance, data: userBalance, loading: loadingBalance } = useRequest(ApiGetUserBalance, {
    manual: true,
    onError: (err) => {
      console.error('获取用户余额信息失败', err)
    }
  })

  const { run: runGetBalanceDetail, data: balanceDetailInfo } = useRequest(ApiBalanceDetailInfo, {
    manual: true,
  })

  // 首存 次存 判断
  const depositTy = computed(() => {
    //目前只有首充
    return 1
    if (userInfo.value.deposit_amount) {
      return 2
    } else {
      return 1
    }
  })

  const { run: runDepositBonusConf, data: depositBonusConf } = useRequest(() => ApiDepositBonusConf({ ty: depositTy.value }), {
    // manual: true,
    onError: (err) => {
      console.error('获取首充信息失败', err)
    },
    onSuccess: (data) =>{
      // console.log(data, "*********")
    }
  })

  const { run: runGetWithdrawConf, data: withdrawConf } = useRequest(ApiGetWithdrawConf, {
    manual: true,
  })


  const userBanks = ref<any[]>([])
  const cardLimit = ref(0);
  const { run: runGetUserBanks } = useRequest(ApiBankList, {
    manual: true,
    onSuccess: (data) => {
      if (data.card_limit){
        cardLimit.value = data.card_limit
      }
      if (data && data.d && data.d.length) {
        userBanks.value = data.d
      } else {
        userBanks.value = []
      }
    }
  })

  const { run: runGetTurnTableInfo, data: turnTableInfo } = useRequest(ApiTurnTableInfo, {
    manual: true,
    onSuccess: (data) => {
      console.log(data)
    }
  })

  const { run: runGetTurnTableSwitch, data: turnTableSwitch } = useRequest(ApiTurnTableSwitch, {
    manual: true,
    onSuccess: (data) => {
      console.log(data)
    }
  })

  const { run:runGetServiceData, data: custService } = useRequest(ApiGetFBTG,{
    manual: true,
    onSuccess: (data) => {
      console.log(data)
    }
  })

  const userInfo = computed(() => {
    return {
      ...memberInfoData.value,
      ...userBalance.value,
      // formatAmount: userBalance.value?.brl || 0,
      formatAmount: UsAmountFormat(userBalance.value?.brl || 0),
      balanceDetailInfo: balanceDetailInfo.value,
    }
  })

  // 设置语言
  const setLanguage = (lang: typeof Languages[keyof typeof Languages]) => {
    console.log(i18n.global.locale, lang)
    language.value = lang
    // i18n.global.locale.value = lang
    i18n.global.locale = lang
  }

  // 设置loginDialogVisible
  const setLoginDialogVisible = (visible: boolean) => {
    loginDialogType.value = 'login'
    loginDialogVisible.value = visible
  }

  // 设置注册弹窗
  const setRegisterDialogVisible = (visible: boolean) => {
    loginDialogType.value = 'register'
    loginDialogVisible.value = visible
  }

  // 设置findPasswordDialogVisible
  const setFindPasswordDialogVisible = (visible: boolean) => {
    findPasswordDialogVisible.value = visible
  }

  // 设置editAvatarDialogVisble
  const setEditAvatarDialogVisble = (visible: boolean) => {
    editAvatarDialogVisble.value = visible
  }

  // 设置findPayPasswordDialogVisible
  const setFindPayPasswordDialogVisible = (visible: boolean) => {
    findPayPasswordDialogVisible.value = visible
  }

  const setAdicionarPixDialogVisible = (visible: boolean) => {
    adicionarPixDialogVisible.value = visible
  }

  //
  const setActiveDialogVisble = (visible: boolean) => {
    activeDialogVisble.value = visible
  }


  //footer
  const setFooterDialogVisble = (visible: boolean) => {
    appFooterVisible.value = visible
  }

  const setUserVipInfo = (userVipInfoData: VipInfoItem) => {
    userVipInfo.value = userVipInfoData
  }

  // 设置token
  const setToken = (t: string) => {
    clearToken();
    localStorage.setItem('t', t)
    token.value = t
    if (t != "") {
      iosInfoShow.value = true
      // isShowGoldCollect.value = true
    }
  }

  // 清除token
  const clearToken = () => {
    localStorage.removeItem('t')
    token.value = ''
  }

  // 存储账户
  const setStoredAccount = (t: string) => {
    localStorage.setItem('sc', t)
    storedAccount.value = t
  }

  // 清理账户
  const clearStoredAccount = () => {
    localStorage.removeItem('sc')
    storedAccount.value = ''
  }

  //设置支付密码已经绑定成功
  const setPay_password = (t: string) => {
    if (memberInfoData && memberInfoData.value) {
      memberInfoData.value.pay_password = t
    }
    userInfo.value.pay_password = t;
    pay_password.value = t
  }


  //footer
  const setIsRefreshShop = (visible: boolean) => {
    isRefreshShop.value = visible
  }

  const setIsShowLoading = (visible: boolean) => {
    isShowLoading.value = visible
  }

  const setIosInfoShow = (visible: boolean) => {
    iosInfoShow.value = visible
  }

  const setIsShowMessage = (visible: boolean, message: string) => {
    isShowMessage.value = visible
    showMessage.value = message
  }


  const setIsShowGoldCollect = (visible: boolean) => {
    isShowGoldCollect.value = visible
  }

  const setLeftMenuState = (visible: boolean) => {
    showLeftMenu.value = visible
    console.log(showLeftMenu);
  }

  const setShowMusicState = (visible: boolean) => {
    showMusicPlayer.value = visible
  }

  const setPayVisble = (visible: boolean) => {
    isShowPay.value = visible
  }

  const setPayRecordsVisble = (visible: boolean) => {
    isShowPayRecords.value = visible;
  }
  // 退出登录弹框
  const setShowLoginOut = (visible: boolean) => {
    showLoginOut.value = visible
  }
  // 跳转充值页面弹框
  const setShowRecharge = (visible: boolean) => {
    showRecharge.value = visible
  }
  //用户协议弹窗
  const setShowPrivacy = (visible: boolean) => {
    privacyShow.value = visible
  }
  //领取奖励弹窗
  const setIsShowBonusMessage = (visible: boolean, message: string) => {
    isShowBonusMessage.value = visible
    showMessage.value = message
    setTimeout(() => {
      isShowBonusMessage.value = !visible
    }, 2000);
  }
  //领取奖励弹窗
  const setIsShowAppBaixar = (visible: boolean) => {
    isShowAppBaixar.value = visible
  }

  //设置显示绑定CPF信息
  const setBindCPFInfoVisible = (visible: boolean,constCPFInfoStr:string="") => {
    bindCPFInfoVisible.value = visible
    constCPFInfo.value = constCPFInfoStr;
  }

  //设置首充弹窗
  const setFirstChargePopupVisble = (visible: boolean) => {
    isFirstChargePopup.value = visible
  }

  //活动开关
  const switchActiveData = ref<switchActiveData[]>([]);

  const { run: runApiActiveSwitchList } = useRequest(() => ApiActiveSwitchList(), {
    onError: (data) => {
    },
    onSuccess: (data) => {
      if (data) {
        console.log("runApiActiveSwitchList", data)
        if (data?.switch_cfg) {
          switchActiveData.value = [...switchActiveData.value, ...data?.switch_cfg!]
          console.log("switchActiveData", switchActiveData);

          switchActiveData.value.forEach((item)=>{
            if (item.active_type == 1){//首充弹窗
              isFirstChargePopup.value = item.status && item.popup_switch;
            }
          })
        }
      }
    }
  })

  return {
    isLogin,
    userInfo,
    depositTy,
    token,
    language,
    setLanguage,
    loginDialogVisible,
    loginDialogType,
    setLoginDialogVisible,
    registerDialogVisible,
    setRegisterDialogVisible,
    findPasswordDialogVisible,
    setFindPasswordDialogVisible,
    editAvatarDialogVisble,
    setEditAvatarDialogVisble,
    setToken,
    clearToken,
    runGetMemberInfo,
    runGetUserBalance,
    loadingBalance,
    loadingUserinfo,
    isApp,
    depositBonusConf,
    runGetBalanceDetail,
    balanceDetailInfo,
    runGetWithdrawConf,
    withdrawConf,
    custService,
    userBanks,
    cardLimit,
    runGetUserBanks,
    turnTableInfo,
    runGetTurnTableInfo,
    turnTableSwitch,
    runGetTurnTableSwitch,
    storedAccount,
    setStoredAccount,
    clearStoredAccount,
    setPay_password,
    pay_password,
    setFindPayPasswordDialogVisible,
    setAdicionarPixDialogVisible,
    findPayPasswordDialogVisible,
    adicionarPixDialogVisible,
    activeDialogVisble,
    setActiveDialogVisble,
    runActivePopupData,
    appFooterVisible,
    setFooterDialogVisble,
    isRefreshShop,
    setIsRefreshShop,
    isShowLoading,
    setIsShowLoading,
    isShowMessage,
    setIsShowMessage,
    showMessage,
    iosInfoShow,
    setIosInfoShow,
    isShowGoldCollect,
    setIsShowGoldCollect,
    setLeftMenuState,
    setShowMusicState,
    showMusicPlayer,
    showLeftMenu,
    curCycleMode,
    isPlayingMusic,
    isShowPay,
    setPayVisble,
    isShowPayRecords,
    setPayRecordsVisble,
    setUserVipInfo,
    userVipInfo,
    showLoginOut,
    setShowLoginOut,
    showRecharge,
    setShowRecharge,
    secletType,
    privacyShow,
    setShowPrivacy,
    isShowBonusMessage,
    setIsShowBonusMessage,
    appRightActiveVisible,
    setIsShowAppBaixar,
    isShowAppBaixar,
    isSmallGameIcon,
    gameItemPageNum,
    getRemoteImgUrl,
    setBindCPFInfoVisible,
    bindCPFInfoVisible,
    constCPFInfo,
    isFirstChargePopup,
    setFirstChargePopupVisble,
    switchActiveData,
    isShowStar,
    isShowGood,
    isShowPGSlots,
    PGSlotsImgType
  }
})
