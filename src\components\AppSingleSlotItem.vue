<script setup lang="ts" name="AppSingleSlotItem">
import { GameNavEnum, GameSoltTypeEnum } from "~/types/common";

interface Props {
  data: any;
}
const router = useRouter();
const route = useRoute();
const props = withDefaults(defineProps<Props>(), {});

// 打开页签-游戏列表
const openPage = () => {
  console.log(8328);

  console.log(props.data);

  if (props.data.img.indexOf("blockchain") !== -1) {
    console.log("/subgame/Blockchain?platform_id=" + props.data.platform_id);
    
    router.push("/subgame/Blockchain?platform_id=" + props.data.platform_id);
    return;
  }

  if (props.data.img.indexOf("AoVivo") !== -1) {
    console.log("/subgame/AoVivo?platform_id=" + props.data.platform_id);
    
    router.push("/subgame/AoVivo?platform_id=" + props.data.platform_id);
    return;
  }

  if (props.data.img.indexOf("Pescaria") !== -1) {
    console.log("/subgame/Pescaria?platform_id=" + props.data.platform_id);
    
    router.push("/subgame/Pescaria?platform_id=" + props.data.platform_id);
    return;
  }



  router.push("/subgame?platform_id=" + props.data.platform_id);
};
</script>

<template>
  <div class="app-single-slot-item">
    <div class="item_content">
      <div
        class="game-img"
        v-lazy:background-image="data.img"
        @click="openPage"
      ></div>
    </div>
  </div>
</template>
<style>
:root {
  --app-single-slot-height: 180px;
  --app-single-slot-width: 100%;
}
</style>
<style lang="scss" scoped>
.app-single-slot-item {
  width: var(--app-single-slot-width);
  height: var(--app-single-slot-height);
  scroll-snap-align: start;
  border-radius: 10px;
  position: relative;
  margin: 15px 0;

  .game-img {
    width: var(--app-single-slot-width);
    height: var(--app-single-slot-height);
    position: absolute;
    top: 0;
    left: 0px;
    transition: opacity 0.5s;
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    opacity: 0;
    object-fit: cover;
    border-radius: 10px;
    overflow: hidden;
    background-size: 100% 100%;
  }

  .game-img[lazy="loading"] {
    opacity: 0;
  }

  .game-img[lazy="error"] {
  }

  .game-img[lazy="loaded"] {
    opacity: 1;
  }

  img.game-img[lazy="loading"] {
    opacity: 0;
  }

  img.game-img[lazy="error"] {
  }

  img.game-img[lazy="loaded"] {
    opacity: 1;
  }

  .item_content {
    width: var(--app-single-slot-width);
    height: var(--app-single-slot-height);
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
