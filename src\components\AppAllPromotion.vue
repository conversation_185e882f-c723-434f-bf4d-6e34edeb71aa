<script setup lang='ts' name='promotion'>
import AppTab from './AppTab.vue';
//import { userInfo } from 'node:os';

const router = useRouter()
const appStore = useAppStore();
const { isApp, userInfo } = storeToRefs(appStore)
const { secletType } = storeToRefs(appStore);

const tabList = ref<any[]>([
  { src: '/icons/icon_all_promotion', promotionId: 0, cateName: "0 games", lastName: "Misto" },

])

const promotionList = ref<any[]>([
  { src: '/img/promotion/ActiveImg0', path: '/promotion-detail/invitation-rewards', promotionId: 0},//邀请奖励
  { src: '/img/promotion/ActiveImg1', path: '/agent', promotionId: 1},//代理
  { src: '/img/promotion/ActiveImg2', path: '/promotion-detail/recharge-rewards', promotionId: 2},//累计存款
  { src: '/img/promotion/ActiveImg3', path: '/promotion-detail/weekly-loss', promotionId: 3},//周亏损
  { src: '/img/promotion/ActiveImg4', path: '/advancement', promotionId: 5},//vip
])

const { data: list } = useRequest(ApiGetMemerBanner)
const banners = computed(() => {
  if (list.value && list.value.length) {
    return list.value.map((i) => {
      let images = i.images.split('&')
      let h5str = images.filter((m: any) => m.indexOf('h5=') != -1)[0]
      i.redirect_url = decodeURIComponent(decodeURIComponent(i.redirect_url))
      if (h5str) {
        i.h5img = h5str.split('=')[1]
      }
      return i
    })
  }
  return []
})

function gotoPromotion(id: number) {

}

function promotionClick(item: any) {
  console.log(JSON.stringify(item))
  if (item.promotionId == 5){//VIP
    secletType.value = 2;
  }else if (item.path) {
    router.push({path: item.path, query: { key: item.promotionId }})
  }
}

function imgClick(type: number, url: string) {

console.log("imgClick =", type, url)
if (!type || !url) return;
switch (type) {
  case 1:
    if (url.includes('key=2')){
      secletType.value = 2;
    }else if(url.includes('key=6')){
      secletType.value = 6;
    }else{
      router.push(url)
    }
    break;
  case 2:
    window.open(url, '_blank')
    break;
}

} 

function gotoHistory() {
  secletType.value = 5;
}

</script>
<template>
  <div class="all-promotion">
    {{ console.log(banners) }}
    <div class="leftDiv">
      <div v-for="item in tabList" class="leftDivBtn" :class="{ active: true }"
        @click="gotoPromotion(item.promotionId)">
        <AppImage class="icon_leftBtn" :src="item.src" alt="" />
        <div class="text">{{ item.lastName }}</div>
      </div>
      <div class="button_div">
        <div class="button_item" :class="{ active: true }" @click="gotoHistory()"> Histórico </div>
      </div>
    </div>

    <div class="rightDiv">
      <!-- <div v-for="item in promotionList" class="promotionItem" @click="promotionClick(item)">
        <AppImage class="active_img" :src="item.src" alt="" />
      </div> -->
      <div v-if="banners.length > 0">
        <div v-for="item in banners" class="promotionItem" @click="() => imgClick(item.url_type, item.redirect_url)">
          <AppImage class="active_img" :src="item.images" alt="" />
        </div>
      </div>
      <div v-else>
        <div v-for="item in promotionList" class="promotionItem">
        </div>
      </div>

    <div class="footer-space"/>
    </div>
  </div>
</template>

<style lang='scss' scoped>
@import '../theme/mixin.scss';

.all-promotion {
    position: absolute;
    width: 100%;
    height: auto;
    background-color: var(--theme-bg-color);
}

.leftDiv {
  display: inline-block;
  width: 187px;
  // background-color: #FFFFFF;
  // height: calc(100vh - 203px);
  // overflow: auto;
  // scrollbar-width: none;
  // height: 1250px;
}

.leftDivBtn {
  width: 150px;
  height: 70px;
  background: var(--theme-top-nav-bg);
  border-radius: 12px;
  margin: 0 auto;
  margin-top: 25px;
  margin-left: 25px;
  padding-top: 5px;
  color: var(--theme-text-color);
  // border: 2px solid;
  border-color: var(--theme-left-nav-text-active-color);
  display: flex;
  justify-content: center;
  /* 水平居中 */
  align-items: center;

  &.active {
    // background: var(--theme-primary-color);
    @include webp('/icons/btn_zc1_1');
    background-size: 150px 70px;
    color: var(--theme-left-nav-text-active-color);

  }


  .icon_leftBtn {
    display: block;
    margin: 0 auto;
    width: 23px;
    // margin-top: 20px;
  }

  .text {
    display: inline-block;
    font-size: 20px;
    width: 90px;
    text-align: center;
    // margin-top: 5px;
  }
}
.button_div {
  // background-color: #FFFFFF;
  margin: auto;
  // width: 500px;
  margin-top: 25px;
  margin-bottom: 25px;
  margin-left: 30px;

  .button_item {
    margin-right: 20px;
    display: inline-block;
    width: 140px;
    height: 56px;
    background: var(--theme-primary-font-color);
    border: 2px solid;
    border-color: var(--theme-primary-color);
    border-radius: 12px;
    color: var(--theme-text-color);
    font-size: 20px;
    text-align: center;
    line-height: 56px;

    &.active {
      background: var(--theme-primary-color);
      color: var(--theme-font-on-background-color);
      // border-radius:6px;
    }
  }


}

.rightDiv {
  float: right;
  height: calc(80vh);
  width: 562px;
  overflow: auto;
  // background-color: #FFFFFF;
}

.promotionItem {
  margin-top: 25px;

  width: 540px;
  height: 260px;
  background: var(--theme-primary-color);
  border-radius: 14px;

  img {
    margin-top: 12px;
    margin-left: 10px;

    width: 520px;
    height: 234px;
  }
}

.active_img{
  margin-top: 12px;
    margin-left: 10px;

    width: 520px;
    height: 234px;
}

.footer-space {
  height: 100px;
  /* 或者任何你需要的高度 */
}
</style>
